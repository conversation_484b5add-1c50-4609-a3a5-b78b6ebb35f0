<?php
/**
 * Temporary script to create the ad campaigns dashboard page
 * Run this once to create the dashboard page
 */

// Include WordPress
require_once( '../../../app/public/wp-config.php' );

// Check if page already exists
$existing_page = get_page_by_path( 'dashboard-ad-campaigns' );
if ( $existing_page ) {
    echo "Dashboard page already exists with ID: " . $existing_page->ID . "\n";
    echo "URL: " . get_permalink( $existing_page->ID ) . "\n";
    exit;
}

// Create the page
$page_data = array(
    'post_title'     => __( 'Ad Campaigns Dashboard', 'houzez-ads-extension' ),
    'post_name'      => 'dashboard-ad-campaigns',
    'post_content'   => __( 'This page is used for the ad campaigns dashboard functionality.', 'houzez-ads-extension' ),
    'post_status'    => 'publish',
    'post_type'      => 'page',
    'post_author'    => 1,
    'comment_status' => 'closed',
    'ping_status'    => 'closed',
);

$page_id = wp_insert_post( $page_data );

if ( $page_id && ! is_wp_error( $page_id ) ) {
    // Set the page template
    update_post_meta( $page_id, '_wp_page_template', 'template/user_dashboard_ad_campaign.php' );
    
    // Store the page ID for future reference
    update_option( 'houzez_ads_dashboard_page_id', $page_id );
    
    echo "Dashboard page created successfully!\n";
    echo "Page ID: " . $page_id . "\n";
    echo "URL: " . get_permalink( $page_id ) . "\n";
} else {
    echo "Error creating dashboard page: " . $page_id->get_error_message() . "\n";
}
