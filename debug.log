[30-Jun-2025 20:06:11 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:06:11 UTC] PHP Stack trace:
[30-Jun-2025 20:06:11 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:11 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:11 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:11 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:11 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:11 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:11 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:06:11 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:06:11 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:06:11 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:06:11 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:06:11 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:06:11 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:06:11 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:06:11 UTC] PHP Stack trace:
[30-Jun-2025 20:06:11 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:11 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:11 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:11 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:11 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:11 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:11 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:06:11 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:06:11 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:06:12 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:06:12 UTC] PHP Stack trace:
[30-Jun-2025 20:06:12 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:12 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:12 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:12 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:12 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:12 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:12 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:06:12 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:06:12 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:06:12 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:06:12 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:06:12 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:06:12 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:06:12 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:06:12 UTC] PHP Stack trace:
[30-Jun-2025 20:06:12 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:12 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:12 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:12 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:12 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:12 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:12 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:06:12 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:06:12 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:06:13 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:06:13 UTC] PHP Stack trace:
[30-Jun-2025 20:06:13 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:13 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:13 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:13 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:13 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:13 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:13 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:06:13 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:06:13 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:06:13 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:06:13 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:06:13 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:06:13 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:06:13 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:06:13 UTC] PHP Stack trace:
[30-Jun-2025 20:06:13 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:13 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:13 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:13 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:13 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:13 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:13 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:06:13 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:06:13 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:06:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:06:14 UTC] PHP Stack trace:
[30-Jun-2025 20:06:14 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:14 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:14 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:14 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:14 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:14 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:14 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:06:14 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:06:14 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:06:14 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:06:14 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:06:14 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:06:14 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:06:14 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:06:14 UTC] PHP Stack trace:
[30-Jun-2025 20:06:14 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:14 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:14 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:14 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:14 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:14 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:14 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:06:14 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:06:14 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:06:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:06:15 UTC] PHP Stack trace:
[30-Jun-2025 20:06:15 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:15 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:15 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:15 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:15 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:15 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:15 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:06:15 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:06:15 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:06:15 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:06:15 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:06:15 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:06:15 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:06:15 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:06:15 UTC] PHP Stack trace:
[30-Jun-2025 20:06:15 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:15 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:15 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:15 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:15 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:15 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:15 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:06:15 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:06:15 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:06:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:06:15 UTC] PHP Stack trace:
[30-Jun-2025 20:06:15 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:15 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:15 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:15 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:15 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:15 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:15 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:06:15 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:06:15 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:06:15 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:06:15 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:06:15 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:06:15 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:06:15 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:06:15 UTC] PHP Stack trace:
[30-Jun-2025 20:06:15 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:06:15 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:06:15 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:06:15 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:06:15 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:06:15 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:06:15 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:06:15 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:06:15 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:09:47 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:09:47 UTC] PHP Stack trace:
[30-Jun-2025 20:09:47 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:0
[30-Jun-2025 20:09:47 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:10
[30-Jun-2025 20:09:47 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin.php:35
[30-Jun-2025 20:09:47 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:09:47 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:09:47 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:09:47 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:09:47 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:09:47 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:09:47 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:09:47 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:09:47 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:09:47 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:09:47 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:09:47 UTC] PHP Stack trace:
[30-Jun-2025 20:09:47 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:0
[30-Jun-2025 20:09:47 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:10
[30-Jun-2025 20:09:47 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin.php:35
[30-Jun-2025 20:09:47 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:09:47 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:09:47 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:09:47 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:09:47 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:09:47 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:09:48 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:09:48 UTC] PHP Stack trace:
[30-Jun-2025 20:09:48 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:09:48 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:09:48 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:09:48 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:09:48 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:09:48 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:09:48 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:09:48 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:09:48 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:09:48 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:09:48 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:09:48 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:09:48 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:09:48 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:09:48 UTC] PHP Stack trace:
[30-Jun-2025 20:09:48 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:09:48 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:09:48 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:09:48 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:09:48 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:09:48 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:09:48 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:09:48 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:09:48 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:09:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:09:51 UTC] PHP Stack trace:
[30-Jun-2025 20:09:51 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:0
[30-Jun-2025 20:09:51 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:10
[30-Jun-2025 20:09:51 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin.php:35
[30-Jun-2025 20:09:51 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:09:51 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:09:51 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:09:51 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:09:51 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:09:51 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:09:51 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:09:51 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:09:51 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:09:51 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:09:51 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:09:51 UTC] PHP Stack trace:
[30-Jun-2025 20:09:51 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:0
[30-Jun-2025 20:09:51 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:10
[30-Jun-2025 20:09:51 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin.php:35
[30-Jun-2025 20:09:51 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:09:51 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:09:51 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:09:51 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:09:51 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:09:51 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:09:51 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:09:51 UTC] PHP Stack trace:
[30-Jun-2025 20:09:51 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:09:51 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:09:51 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:09:51 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:09:51 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:09:51 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:09:51 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:09:51 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:09:51 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:09:51 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:09:51 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:09:51 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:09:51 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:09:51 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:09:51 UTC] PHP Stack trace:
[30-Jun-2025 20:09:51 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:09:51 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:09:51 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:09:51 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:09:51 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:09:51 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:09:51 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:09:51 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:09:51 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:10:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:10:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:10:14 UTC] PHP Stack trace:
[30-Jun-2025 20:10:14 UTC] PHP Stack trace:
[30-Jun-2025 20:10:14 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[30-Jun-2025 20:10:14 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[30-Jun-2025 20:10:14 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[30-Jun-2025 20:10:14 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:10:14 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[30-Jun-2025 20:10:14 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:10:14 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:10:14 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:10:14 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:10:14 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:10:14 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:10:14 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:10:14 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:10:14 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:10:14 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:10:14 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:10:14 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:10:14 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:10:14 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:10:14 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:10:14 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:10:14 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:10:14 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:10:14 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:10:14 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:10:14 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:10:14 UTC] PHP Stack trace:
[30-Jun-2025 20:10:14 UTC] PHP Stack trace:
[30-Jun-2025 20:10:14 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[30-Jun-2025 20:10:14 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[30-Jun-2025 20:10:14 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[30-Jun-2025 20:10:14 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[30-Jun-2025 20:10:14 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:10:14 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:10:14 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:10:14 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:10:14 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:10:14 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:10:14 UTC] PHP   6. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:10:14 UTC] PHP   6. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:10:14 UTC] PHP   7. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:10:14 UTC] PHP   7. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:10:14 UTC] PHP   8. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:10:14 UTC] PHP   8. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:10:14 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:10:14 UTC] PHP Stack trace:
[30-Jun-2025 20:10:14 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[30-Jun-2025 20:10:14 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[30-Jun-2025 20:10:14 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:10:14 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:10:14 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:10:14 UTC] PHP   6. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:10:14 UTC] PHP   7. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:10:14 UTC] PHP   8. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:10:14 UTC] PHP   9. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:10:14 UTC] PHP  10. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:10:14 UTC] PHP  11. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:10:14 UTC] PHP  12. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:10:14 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:10:14 UTC] PHP Stack trace:
[30-Jun-2025 20:10:14 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:0
[30-Jun-2025 20:10:14 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-cron.php:46
[30-Jun-2025 20:10:14 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:10:14 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:10:14 UTC] PHP   5. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:10:14 UTC] PHP   6. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:10:14 UTC] PHP   7. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:10:14 UTC] PHP   8. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:10:15 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:10:15 UTC] PHP Stack trace:
[30-Jun-2025 20:10:15 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:0
[30-Jun-2025 20:10:15 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:10
[30-Jun-2025 20:10:15 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin.php:35
[30-Jun-2025 20:10:15 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:10:15 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:10:15 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:10:15 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:10:15 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:10:15 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:10:15 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:10:15 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:10:15 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:10:15 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:10:15 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:10:15 UTC] PHP Stack trace:
[30-Jun-2025 20:10:15 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:0
[30-Jun-2025 20:10:15 UTC] PHP   2. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/index.php:10
[30-Jun-2025 20:10:15 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-admin/admin.php:35
[30-Jun-2025 20:10:15 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:10:15 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:10:15 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:10:15 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:10:15 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:10:15 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
[30-Jun-2025 20:10:16 UTC] PHP Notice:  Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in version 6.7.0.) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php on line 6121
[30-Jun-2025 20:10:16 UTC] PHP Stack trace:
[30-Jun-2025 20:10:16 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:10:16 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:10:16 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:10:16 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:10:16 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:10:16 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:10:16 UTC] PHP   7. _x($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:109
[30-Jun-2025 20:10:16 UTC] PHP   8. translate_with_gettext_context($text = 'Campaigns', $context = 'Post type general name', $domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:410
[30-Jun-2025 20:10:16 UTC] PHP   9. get_translations_for_domain($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:262
[30-Jun-2025 20:10:16 UTC] PHP  10. _load_textdomain_just_in_time($domain = 'houzez-ads-extension') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1409
[30-Jun-2025 20:10:16 UTC] PHP  11. _doing_it_wrong($function_name = '_load_textdomain_just_in_time', $message = 'Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later.', $version = '6.7.0') /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/l10n.php:1371
[30-Jun-2025 20:10:16 UTC] PHP  12. wp_trigger_error($function_name = '', $message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = *uninitialized*) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6061
[30-Jun-2025 20:10:16 UTC] PHP  13. trigger_error($message = 'Function _load_textdomain_just_in_time was called <strong>incorrectly</strong>. Translation loading for the <code>houzez-ads-extension</code> domain was triggered too early. This is usually an indicator for some code in the plugin or theme running too early. Translations should be loaded at the <code>init</code> action or later. Please see <a href="https://developer.wordpress.org/advanced-administration/debug/debug-wordpress/">Debugging in WordPress</a> for more information. (This message was added in versi'..., $error_level = 1024) /Users/<USER>/Local Sites/ads-manager/app/public/wp-includes/functions.php:6121
[30-Jun-2025 20:10:16 UTC] PHP Fatal error:  Cannot redeclare houzez_ads_user_can_create_campaigns() (previously declared in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php:39) in /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/helpers.php on line 251
[30-Jun-2025 20:10:16 UTC] PHP Stack trace:
[30-Jun-2025 20:10:16 UTC] PHP   1. {main}() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:0
[30-Jun-2025 20:10:16 UTC] PHP   2. require() /Users/<USER>/Local Sites/ads-manager/app/public/index.php:17
[30-Jun-2025 20:10:16 UTC] PHP   3. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-blog-header.php:13
[30-Jun-2025 20:10:16 UTC] PHP   4. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-load.php:50
[30-Jun-2025 20:10:16 UTC] PHP   5. require_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-config.php:106
[30-Jun-2025 20:10:16 UTC] PHP   6. include_once() /Users/<USER>/Local Sites/ads-manager/app/public/wp-settings.php:545
[30-Jun-2025 20:10:16 UTC] PHP   7. run_houzez_ads_extension() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:168
[30-Jun-2025 20:10:16 UTC] PHP   8. Houzez_Ads_Extension->__construct() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/houzez-ads-extension.php:165
[30-Jun-2025 20:10:16 UTC] PHP   9. Houzez_Ads_Extension->load_dependencies() /Users/<USER>/Local Sites/ads-manager/app/public/wp-content/plugins/houzez-ads-extension/includes/class-houzez-ads-extension.php:60
