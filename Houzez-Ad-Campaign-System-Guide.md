# Houzez Ad Campaign System Extension - Complete Guide

## Table of Contents
1. [Overview](#overview)
2. [Installation & Setup](#installation--setup)
3. [Admin Guide](#admin-guide)
4. [User Guide](#user-guide)
5. [WooCommerce Integration](#woocommerce-integration)
6. [Troubleshooting](#troubleshooting)
7. [Developer Reference](#developer-reference)

---

## Overview

The Houzez Ad Campaign System Extension is a comprehensive advertising solution designed specifically for the Houzez real estate theme. It allows property agents, agencies, and businesses to create and manage promotional campaigns for their properties, profiles, and services.

### Key Features
- **Multiple Ad Types**: Property promotion, agent/agency profiles, and business partnerships
- **Strategic Ad Zones**: Homepage banners, sidebar ads, search results, and property detail pages
- **Dynamic Pricing**: Flexible pricing based on zone, duration, and quantity
- **WooCommerce Integration**: Complete e-commerce functionality for purchasing campaigns
- **Analytics & Tracking**: Detailed impression and click tracking with CTR metrics
- **User Dashboard**: Integrated with <PERSON><PERSON><PERSON> dashboard for seamless user experience
- **Admin Management**: Comprehensive admin interface for campaign oversight

### System Requirements
- WordPress 5.0+
- Houzez Theme (latest version recommended)
- WooCommerce 3.0+ (for e-commerce features)
- PHP 7.4+
- MySQL 5.6+

---

## Installation & Setup

### 1. Plugin Installation

#### Method 1: WordPress Admin Upload
1. Download the plugin ZIP file
2. Go to **WordPress Admin → Plugins → Add New**
3. Click **Upload Plugin**
4. Select the ZIP file and click **Install Now**
5. Click **Activate Plugin**

#### Method 2: FTP Upload
1. Extract the plugin ZIP file
2. Upload the `houzez-ads-extension` folder to `/wp-content/plugins/`
3. Go to **WordPress Admin → Plugins**
4. Find "Houzez Ad Campaign System" and click **Activate**

### 2. Initial Configuration

#### Required Dependencies Check
Upon activation, the plugin will check for:
- ✅ **Houzez Theme**: Required for optimal functionality
- ⚠️ **WooCommerce**: Optional but recommended for e-commerce features

#### Database Setup
The plugin automatically creates:
- Custom post type: `banner_campaign`
- Analytics table: `wp_houzez_ad_analytics`
- Default pricing structure
- Currency settings

### 3. Basic Settings

#### Pricing Configuration
Navigate to **Ad Campaigns → Settings** to configure:

```
Default Pricing Structure:
├── Homepage Banner
│   ├── 7 days: $49
│   ├── 14 days: $89
│   └── 30 days: $149
├── Sidebar Ads
│   ├── 7 days: $29
│   ├── 14 days: $49
│   └── 30 days: $89
├── Search Results
│   ├── 7 days: $10
│   ├── 14 days: $18
│   └── 30 days: $30
└── Property Detail
    ├── 7 days: $35
    ├── 14 days: $59
    └── 30 days: $99
```

#### Currency Settings
- **Currency Symbol**: $ (default)
- **Currency Position**: Before amount
- **Auto-approval**: Disabled (manual review required)

---

## Admin Guide

### Campaign Management

#### Accessing Campaign Dashboard
1. Go to **WordPress Admin → Ad Campaigns**
2. View all campaigns with status indicators:
   - 🟢 **Approved**: Active and running
   - 🟡 **Pending**: Awaiting approval
   - 🔴 **Rejected**: Declined campaigns
   - ⚫ **Expired**: Completed campaigns

#### Campaign List Overview
The campaign list displays:
- **Banner Preview**: Thumbnail of campaign image
- **Campaign Title**: User-defined campaign name
- **Ad Type**: Property, Profile, or Partner
- **Zone**: Target advertising location
- **Duration**: Campaign length in days
- **Status**: Current approval status
- **Analytics**: Views, clicks, and CTR
- **Actions**: Edit, approve, reject, delete

#### Reviewing Campaigns

##### Approval Process
1. **Click on campaign title** to open edit screen
2. **Review campaign details**:
   - Ad type and target zone
   - Selected properties (for property ads)
   - Banner image and link
   - Campaign duration and pricing
3. **Check content compliance**:
   - Appropriate imagery
   - Valid target URLs
   - Accurate property information
4. **Set campaign status**:
   - **Approved**: Campaign goes live
   - **Pending**: Keep under review
   - **Rejected**: Decline with reason

##### Campaign Analytics
Monitor performance metrics:
- **Impressions**: Number of times ad was displayed
- **Clicks**: Number of times ad was clicked
- **CTR (Click-Through Rate)**: Clicks ÷ Impressions × 100
- **Revenue**: Total earnings from campaign

#### Bulk Actions
Select multiple campaigns to:
- **Approve** multiple campaigns
- **Reject** campaigns in bulk
- **Delete** expired campaigns
- **Export** analytics data

### WooCommerce Product Management

#### Ad Campaign Products
The plugin automatically creates WooCommerce products for each ad zone:
- **Homepage Banner Product**
- **Sidebar Ad Product**
- **Search Results Product**
- **Property Detail Product**

#### Product Configuration
For each product, configure:
- **Pricing**: Base price for different durations
- **Inventory**: Set stock levels if needed
- **Descriptions**: Clear explanation of ad placement
- **Images**: Visual representation of ad zones

#### Order Management
When users purchase campaigns:
1. **Order Created**: Standard WooCommerce order
2. **Campaign Generated**: Automatic campaign creation
3. **Status Sync**: Order status affects campaign status
4. **Payment Processing**: Standard WooCommerce flow

### Settings & Configuration

#### General Settings
- **Auto-approval**: Enable/disable automatic campaign approval
- **Rotation Interval**: How often ads rotate (default: 30 minutes)
- **Currency Settings**: Symbol and position
- **Email Notifications**: Configure admin alerts

#### Zone Management
Configure ad zones:
- **Zone Names**: Customize zone labels
- **Pricing Tiers**: Set different price points
- **Display Rules**: Control when/where ads appear
- **Size Restrictions**: Set image dimension requirements

#### User Permissions
Control who can create campaigns:
- **Administrators**: Full access
- **Agents**: Can create property campaigns
- **Agencies**: Can create profile campaigns
- **Custom Roles**: Define specific permissions

---

## User Guide

### Getting Started

#### Accessing the Campaign Dashboard
1. **Log in** to your account
2. **Navigate to Dashboard** (usually `/dashboard/`)
3. **Click "Ad Campaigns"** in the sidebar menu

#### Dashboard Overview
The user dashboard shows:
- **Campaign Statistics**: Total campaigns, active campaigns, views, clicks
- **Recent Campaigns**: Latest campaign activity
- **Quick Actions**: Create new campaign, view analytics
- **Account Status**: Current subscription or credit balance

### Creating Your First Campaign

#### Step 1: Choose Campaign Type
Select from three campaign types:

##### Property Promotion
- **Purpose**: Promote specific properties
- **Best For**: Highlighting featured listings
- **Targeting**: Property search results, detail pages
- **Pricing**: Based on number of properties selected

##### Agent/Agency Profile
- **Purpose**: Promote your professional profile
- **Best For**: Building brand awareness
- **Targeting**: Homepage, sidebar placements
- **Pricing**: Fixed rate per duration

##### Business Partnership
- **Purpose**: Promote related services
- **Best For**: Mortgage brokers, contractors, etc.
- **Targeting**: All zones available
- **Pricing**: Premium rates for business ads

#### Step 2: Select Ad Zone
Choose where your ad will appear:

##### Homepage Banner
- **Location**: Top of homepage
- **Visibility**: Highest traffic area
- **Size**: Large banner format
- **Best For**: Major announcements, featured properties

##### Sidebar Ads
- **Location**: Right sidebar on various pages
- **Visibility**: Consistent across site
- **Size**: Medium rectangular format
- **Best For**: Ongoing promotions, profile ads

##### Search Results
- **Location**: Within property search listings
- **Visibility**: Targeted to property searchers
- **Size**: Listing-style format
- **Best For**: Property promotions, competitive positioning

##### Property Detail Pages
- **Location**: Individual property pages
- **Visibility**: Targeted to property viewers
- **Size**: Banner or sidebar format
- **Best For**: Related properties, services

#### Step 3: Configure Campaign Details

##### Basic Information
- **Campaign Title**: Internal reference name
- **Campaign Description**: Optional notes for your reference

##### Duration & Scheduling
Choose between:
- **Preset Durations**: 7, 14, or 30 days
- **Custom Dates**: Specific start and end dates

##### Content Upload
- **Banner Image**: Upload high-quality image (recommended sizes vary by zone)
- **Target URL**: Where clicks should redirect
- **Alt Text**: Accessibility description

##### Property Selection (Property Campaigns Only)
- **Select Properties**: Choose from your published listings
- **Quantity Pricing**: Price increases with number of properties
- **Property Preview**: See how properties will appear

#### Step 4: Review & Purchase

##### Pricing Summary
Review calculated pricing:
- **Base Price**: Zone and duration cost
- **Quantity Multiplier**: Additional properties
- **Total Cost**: Final campaign price
- **Payment Method**: Credit card, PayPal, etc.

##### Campaign Preview
- **Visual Preview**: See how your ad will appear
- **Placement Preview**: Understand ad positioning
- **Mobile Preview**: Check mobile responsiveness

##### Terms & Conditions
- **Content Guidelines**: Ensure compliance
- **Refund Policy**: Understand cancellation terms
- **Approval Process**: Timeline expectations

### Managing Active Campaigns

#### Campaign Dashboard
Monitor your campaigns:
- **Status Indicators**: Visual status representation
- **Performance Metrics**: Real-time analytics
- **Remaining Duration**: Days left in campaign
- **Action Buttons**: Edit, pause, extend options

#### Analytics & Reporting

##### Performance Metrics
Track campaign success:
- **Impressions**: How many people saw your ad
- **Clicks**: How many people clicked your ad
- **CTR**: Click-through rate percentage
- **Cost Per Click**: Efficiency metric

##### Detailed Analytics
- **Hourly Breakdown**: Performance by time of day
- **Daily Trends**: Performance over campaign duration
- **Geographic Data**: Where clicks originated
- **Device Breakdown**: Desktop vs mobile performance

#### Campaign Optimization

##### A/B Testing
- **Image Variations**: Test different banner images
- **Copy Testing**: Try different headlines
- **Target URL Testing**: Optimize landing pages
- **Timing Tests**: Find optimal campaign periods

##### Performance Improvement
- **Low CTR**: Consider new imagery or messaging
- **High Impressions, Low Clicks**: Improve call-to-action
- **Low Impressions**: Consider higher-traffic zones
- **Budget Optimization**: Reallocate spend to top performers

### Payment & Billing

#### Payment Methods
Supported payment options:
- **Credit/Debit Cards**: Visa, MasterCard, American Express
- **PayPal**: Direct PayPal integration
- **Bank Transfer**: For larger campaigns
- **Account Credits**: Prepaid campaign credits

#### Billing Cycle
- **Pay-per-Campaign**: Individual campaign purchases
- **Monthly Subscriptions**: Recurring campaign allowances
- **Annual Plans**: Discounted yearly packages
- **Enterprise Accounts**: Custom billing arrangements

#### Invoicing & Receipts
- **Automatic Receipts**: Email confirmation for all purchases
- **Detailed Invoices**: Itemized billing statements
- **Tax Documentation**: VAT/sales tax compliance
- **Expense Reporting**: Business-friendly documentation

---

## WooCommerce Integration

### E-commerce Features

#### Product Catalog
The plugin creates WooCommerce products for:
- **Ad Zone Products**: One product per advertising zone
- **Duration Variations**: Different pricing for campaign lengths
- **Quantity Options**: Multiple property selections
- **Add-on Services**: Premium features and extensions

#### Shopping Cart Experience
- **Dynamic Pricing**: Real-time price calculation
- **Campaign Preview**: Visual representation in cart
- **Bulk Discounts**: Savings for multiple campaigns
- **Coupon Support**: Promotional codes and discounts

#### Checkout Process
- **Standard WooCommerce**: Familiar checkout experience
- **Guest Checkout**: No account required option
- **Multiple Payment Gateways**: Various payment methods
- **Order Confirmation**: Detailed purchase summary

### Order Management

#### Order Processing
1. **Order Placed**: Customer completes purchase
2. **Payment Verification**: Gateway confirms payment
3. **Campaign Creation**: Automatic campaign generation
4. **Admin Notification**: Alert sent to administrators
5. **Customer Confirmation**: Receipt and campaign details

#### Order Status Integration
- **Pending Payment**: Campaign created but inactive
- **Processing**: Payment confirmed, campaign under review
- **Completed**: Campaign approved and active
- **Cancelled**: Campaign cancelled, refund processed
- **Refunded**: Campaign stopped, payment returned

#### HPOS Compatibility
The plugin is fully compatible with WooCommerce's High-Performance Order Storage:
- **Modern Architecture**: Optimized for large stores
- **Improved Performance**: Faster order processing
- **Scalability**: Handles high-volume transactions
- **Future-Proof**: Ready for WooCommerce updates

### Advanced E-commerce Features

#### Subscription Support
- **Recurring Campaigns**: Automatic campaign renewal
- **Subscription Management**: Customer self-service portal
- **Flexible Billing**: Monthly, quarterly, annual options
- **Upgrade/Downgrade**: Easy plan modifications

#### Inventory Management
- **Zone Capacity**: Limit concurrent campaigns per zone
- **Time Slot Booking**: Reserve specific campaign periods
- **Availability Calendar**: Visual scheduling interface
- **Waitlist System**: Queue for popular time slots

#### Reporting & Analytics
- **Sales Reports**: Revenue by zone, duration, customer
- **Customer Analytics**: Lifetime value, repeat purchases
- **Product Performance**: Best-selling campaign types
- **Seasonal Trends**: Peak advertising periods

---

## Troubleshooting

### Common Issues

#### Plugin Activation Problems

**Issue**: "There has been a critical error on this website"
**Causes**:
- PHP version incompatibility
- Memory limit exceeded
- Plugin conflicts
- Missing dependencies

**Solutions**:
1. **Check PHP Version**: Ensure PHP 7.4 or higher
2. **Increase Memory Limit**: Add `ini_set('memory_limit', '256M');` to wp-config.php
3. **Deactivate Conflicting Plugins**: Test with minimal plugin set
4. **Check Error Logs**: Review WordPress debug logs

**Issue**: Plugin features not working
**Causes**:
- Houzez theme not active
- WooCommerce not installed
- Incorrect permissions
- Database issues

**Solutions**:
1. **Verify Theme**: Ensure Houzez theme is active
2. **Install WooCommerce**: Required for e-commerce features
3. **Check Permissions**: Verify user roles and capabilities
4. **Database Repair**: Run WordPress database repair tool

#### Campaign Creation Issues

**Issue**: Price not displaying
**Causes**:
- JavaScript errors
- AJAX conflicts
- Caching issues
- Missing pricing data

**Solutions**:
1. **Clear Cache**: Purge all caching plugins
2. **Check Browser Console**: Look for JavaScript errors
3. **Disable Plugins**: Test with minimal plugin set
4. **Reset Pricing**: Deactivate and reactivate plugin

**Issue**: Images not uploading
**Causes**:
- File size limits
- Permission issues
- Unsupported formats
- Server restrictions

**Solutions**:
1. **Check File Size**: Ensure images under upload limit
2. **Verify Permissions**: Check wp-content/uploads permissions
3. **Use Supported Formats**: JPG, PNG, GIF only
4. **Contact Host**: May need server configuration changes

#### Display Problems

**Issue**: Ads not showing on frontend
**Causes**:
- Campaign not approved
- Zone configuration issues
- Theme conflicts
- Caching problems

**Solutions**:
1. **Check Campaign Status**: Ensure campaigns are approved
2. **Verify Zone Settings**: Confirm zone configuration
3. **Test Theme Compatibility**: Switch to default theme temporarily
4. **Clear All Caches**: Including CDN and server-level caching

**Issue**: Analytics not tracking
**Causes**:
- JavaScript disabled
- Ad blockers
- Database issues
- Tracking conflicts

**Solutions**:
1. **Test Without Ad Blockers**: Disable browser extensions
2. **Check JavaScript**: Ensure JS is enabled
3. **Database Verification**: Check analytics table exists
4. **Plugin Conflicts**: Test with other analytics plugins disabled

### Performance Optimization

#### Database Optimization
- **Regular Cleanup**: Remove expired campaigns and old analytics
- **Index Optimization**: Ensure proper database indexing
- **Query Optimization**: Monitor slow queries
- **Backup Strategy**: Regular database backups

#### Caching Configuration
- **Page Caching**: Configure to exclude dynamic ad content
- **Object Caching**: Use Redis or Memcached for better performance
- **CDN Setup**: Serve static assets from CDN
- **Image Optimization**: Compress and optimize ad images

#### Server Requirements
- **PHP Memory**: Minimum 256MB, recommended 512MB
- **Database**: MySQL 5.7+ or MariaDB 10.2+
- **Web Server**: Apache 2.4+ or Nginx 1.14+
- **SSL Certificate**: Required for secure payments

### Getting Support

#### Documentation Resources
- **Plugin Documentation**: Comprehensive guides and tutorials
- **Video Tutorials**: Step-by-step video instructions
- **FAQ Section**: Common questions and answers
- **Community Forum**: User discussions and solutions

#### Technical Support
- **Support Ticket System**: Direct technical assistance
- **Email Support**: General inquiries and feedback
- **Live Chat**: Real-time support during business hours
- **Phone Support**: Premium support for enterprise customers

#### Developer Resources
- **API Documentation**: Integration guides for developers
- **Code Examples**: Sample implementations
- **GitHub Repository**: Source code and issue tracking
- **Developer Forum**: Technical discussions and contributions

---

## Developer Reference

### Hooks & Filters

#### Action Hooks
```php
// Campaign lifecycle hooks
do_action( 'houzez_ads_campaign_created', $campaign_id );
do_action( 'houzez_ads_campaign_approved', $campaign_id );
do_action( 'houzez_ads_campaign_rejected', $campaign_id );
do_action( 'houzez_ads_campaign_expired', $campaign_id );

// Display hooks
do_action( 'houzez_ads_before_banner', $zone, $campaign );
do_action( 'houzez_ads_after_banner', $zone, $campaign );

// Analytics hooks
do_action( 'houzez_ads_impression_tracked', $campaign_id );
do_action( 'houzez_ads_click_tracked', $campaign_id );
```

#### Filter Hooks
```php
// Pricing filters
$price = apply_filters( 'houzez_ads_campaign_price', $price, $zone, $duration, $quantity );
$zones = apply_filters( 'houzez_ads_available_zones', $zones );
$durations = apply_filters( 'houzez_ads_available_durations', $durations );

// Display filters
$banner_html = apply_filters( 'houzez_ads_banner_html', $html, $campaign );
$zones_display = apply_filters( 'houzez_ads_zones_display', $zones );

// Permission filters
$can_create = apply_filters( 'houzez_ads_user_can_create', $can_create, $user_id );
$can_approve = apply_filters( 'houzez_ads_user_can_approve', $can_approve, $user_id );
```

### API Functions

#### Campaign Management
```php
// Create new campaign
$campaign = new Houzez_Banner_Campaign();
$result = $campaign->save( $campaign_data );

// Get campaign by ID
$campaign = new Houzez_Banner_Campaign( $campaign_id );

// Check if campaign is active
$is_active = houzez_ads_is_campaign_active( $campaign_id );

// Get user's campaigns
$campaigns = houzez_ads_get_user_campaigns( $user_id );
```

#### Pricing Functions
```php
// Calculate campaign price
$price = houzez_ads_calculate_campaign_price( $zone, $duration, $quantity, $type );

// Format price for display
$formatted = houzez_ads_format_price( $price );

// Get available zones
$zones = houzez_ads_get_available_zones();

// Get available durations
$durations = houzez_ads_get_available_durations();
```

#### Analytics Functions
```php
// Track impression
houzez_ads_track_impression( $campaign_id );

// Track click
houzez_ads_track_click( $campaign_id );

// Get campaign analytics
$analytics = houzez_ads_get_campaign_analytics( $campaign_id );

// Generate tracking URL
$url = houzez_ads_generate_tracking_url( $campaign_id, $target_url );
```

### Database Schema

#### Campaigns Table (wp_posts)
```sql
-- Campaigns are stored as custom post type 'banner_campaign'
-- Meta fields stored in wp_postmeta:
_houzez_ad_type          -- 'property', 'profile', 'partner'
_houzez_ad_zone          -- 'homepage', 'sidebar', 'search', 'property_detail'
_houzez_duration         -- Campaign duration in days
_houzez_selected_properties -- Serialized array of property IDs
_houzez_banner_image     -- Banner image URL
_houzez_banner_link      -- Target URL
_houzez_banner_alt       -- Alt text
_houzez_start_date       -- Campaign start date
_houzez_end_date         -- Campaign end date
_houzez_campaign_status  -- 'pending', 'approved', 'rejected', 'expired'
_houzez_campaign_price   -- Campaign price
_houzez_order_id         -- Associated WooCommerce order ID
```

#### Analytics Table (wp_houzez_ad_analytics)
```sql
CREATE TABLE wp_houzez_ad_analytics (
    id mediumint(9) NOT NULL AUTO_INCREMENT,
    campaign_id bigint(20) NOT NULL,
    event_type varchar(20) NOT NULL,
    user_id bigint(20) DEFAULT NULL,
    ip_address varchar(45) DEFAULT NULL,
    user_agent text DEFAULT NULL,
    referrer text DEFAULT NULL,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    KEY campaign_id (campaign_id),
    KEY event_type (event_type),
    KEY created_at (created_at)
);
```

### Custom Post Type Registration
```php
register_post_type( 'banner_campaign', array(
    'labels' => array(
        'name' => 'Ad Campaigns',
        'singular_name' => 'Ad Campaign',
        // ... other labels
    ),
    'public' => false,
    'show_ui' => true,
    'show_in_menu' => true,
    'capability_type' => 'post',
    'supports' => array( 'title', 'editor', 'author', 'thumbnail' ),
    'menu_icon' => 'dashicons-megaphone',
) );
```

### Integration Examples

#### Custom Zone Implementation
```php
// Add custom ad zone
add_filter( 'houzez_ads_available_zones', function( $zones ) {
    $zones['footer'] = __( 'Footer Banner', 'your-textdomain' );
    return $zones;
});

// Display ads in custom zone
function display_footer_ads() {
    $campaign = houzez_ads_get_rotated_campaign( 'footer' );
    if ( $campaign ) {
        echo houzez_ads_render_banner( $campaign );
    }
}
add_action( 'wp_footer', 'display_footer_ads' );
```

#### Custom Pricing Logic
```php
// Modify pricing calculation
add_filter( 'houzez_ads_campaign_price', function( $price, $zone, $duration, $quantity ) {
    // Apply bulk discount for multiple properties
    if ( $quantity > 5 ) {
        $price *= 0.9; // 10% discount
    }

    // Premium pricing for homepage
    if ( $zone === 'homepage' ) {
        $price *= 1.5;
    }

    return $price;
}, 10, 4 );
```

#### Analytics Integration
```php
// Send analytics to external service
add_action( 'houzez_ads_click_tracked', function( $campaign_id ) {
    $campaign = new Houzez_Banner_Campaign( $campaign_id );

    // Send to Google Analytics
    wp_remote_post( 'https://www.google-analytics.com/collect', array(
        'body' => array(
            'v' => 1,
            'tid' => 'UA-XXXXXXX-X',
            'cid' => uniqid(),
            't' => 'event',
            'ec' => 'Ad Campaign',
            'ea' => 'Click',
            'el' => $campaign->title,
        )
    ));
});
```

---

## Conclusion

The Houzez Ad Campaign System Extension provides a comprehensive advertising solution for real estate websites. With its intuitive interface, powerful features, and seamless integration with Houzez and WooCommerce, it enables both administrators and users to create, manage, and optimize advertising campaigns effectively.

For additional support, updates, and resources, please visit our documentation website or contact our support team.

---

**Version**: 1.0.0
**Last Updated**: December 2024
**Compatibility**: WordPress 5.0+, Houzez Theme, WooCommerce 3.0+
