# Houzez Ad Campaign System – Extension Guide

> Extend your existing “Real Estate Banner Ads Manager” WordPress plugin into a targeted ad campaign system tailored for the Houzez theme — with full integration into its CRM UI, WooCommerce payments, and frontend display logic.

## Project Goal

Transform your existing standalone plugin into a **targeted ad campaign system** for agents, agencies, and promoters that integrates directly with <PERSON><PERSON><PERSON>’s dashboard, UI, and existing property submission logic — without city targeting (for now).

## Key Features to Add/Change

| Feature                          | Status | Action                          |
|----------------------------------|:------:|---------------------------------|
| Reuse Houzez dashboard UI        | ❌     | Use existing dashboard structure |
| Reuse Houzez property form UX    | ❌     | Mirror field layout/styles       |
| Create ad campaign type          | ❌     | With ad_type, duration, zone     |
| Dynamic WooCommerce pricing      | ❌     | Based on zone, duration, qty     |
| Zone rotation logic              | ❌     | Rotates every 30 minutes         |
| Sponsored property insertion     | ❌     | Every 5th result in search loop  |
| Analytics (views + clicks + CTR) | ✅     | Extend with visual reporting     |
| Campaign approval system         | ✅     | Extend admin columns and filters |

## AI Task Runner Prompt

You're extending a WordPress plugin for a real estate site using the Houzez theme.

Use the existing Houzez CRM dashboard UI and property submission form as the base layout for a new "Ad Campaign" section. Reuse form styles, upload fields, and notification systems where possible. Ads are paid via WooCommerce using a dynamic pricing model based on ad zone, duration, and optional property count.

Inject ads using shortcodes and native Houzez template hooks. Begin by creating a campaign model with fields for ad_type, ad_zone, duration, and selected_properties[].

### Folder Structure

```
houzez-ads-extension/
├── includes/
│   ├── pricing.php                 # Dynamic pricing helper
│   ├── targeting.php               # Rotation, injection logic
│   └── helpers.php                 # Miscellaneous utilities
├── frontend/
│   ├── partials/upload-form.php    # Custom ad form using Houzez UI
│   ├── class-banner-ads-frontend.php
├── models/
│   └── class-banner-campaign.php   # Extended campaign logic
├── woocommerce/
│   └── class-banner-ads-woocommerce.php
├── admin/
│   └── class-banner-ads-admin.php  # Add filters/columns
└── README.md
```

### Commit-Style Task List

#### Plugin Setup
- chore: Add includes/pricing.php to calculate ad cost
- chore: Add includes/targeting.php to rotate ads by zone
- chore: Register shared utilities for ad rendering

#### Ad Campaign Creation
- feat: Extend upload-form.php to include:
  - ad_type: property, profile, partner
  - ad_zone: homepage, sidebar, search, property_detail
  - duration: 7/14/30 days
  - selected_properties[]: optional, based on ad_type
- feat: Save form fields to campaign post meta
- feat: Integrate form into Houzez dashboard using existing layout/components
- feat: Display dynamic pricing before submission

#### Pricing Logic
- feat: Build calculate_campaign_price() to return price by:
  - zone
  - duration
  - quantity (for properties)
- feat: Connect dynamic price to WooCommerce checkout via class-banner-ads-woocommerce.php
- feat: Allow post-checkout status update to “pending approval”

#### Pricing Table Example

| Zone                  | Duration | Price               |
|-----------------------|----------|---------------------|
| Homepage              | 7 days   | $49                 |
| Sidebar (Agency)      | 7 days   | $29                 |
| Property Loop         | weekly   | $10/property/week   |
| Property Detail Page  | weekly   | $35/week            |

#### Frontend Ad Logic
- feat: Rotate homepage ads every 30 minutes using transients
- feat: Modify [banner_ads_zone] shortcode to support ad_zone and rotate ads
- feat: Insert promoted properties into Houzez search loop (every 5th result)
- feat: Show profile ads in sidebar
- feat: Add ad below contact form on property detail page (by type)

#### Analytics
- feat: Track impressions (when 50% visible) and clicks (via redirect)
- feat: Calculate CTR = (clicks / impressions) * 100
- feat: Reuse Houzez “My Properties” analytics layout in dashboard for campaigns

#### Admin UI Extensions
- feat: Add campaign columns to admin list:
  - ad_zone, ad_type, duration, status
- feat: Enable filters by ad type and approval state
- feat: Add bulk Approve / Reject actions
- feat: Auto-publish ad if auto-approval setting is enabled

#### Refactor & Cleanup
- refactor: Remove unused zones like footer
- refactor: Align CSS for ad banners with Houzez’s design
- refactor: Merge fixed Woo products into dynamic pricing logic
- refactor: Clean up shortcode styling to match Houzez theme

### Testing Checklist

| Feature                          | Tested? |
|----------------------------------|:-------:|
| Campaign creation with form UX   | ☐       |
| WooCommerce payment and pricing  | ☐       |
| Admin approval + filters         | ☐       |
| Ad visibility in correct zones   | ☐       |
| Rotation logic working correctly | ☐       |
| Analytics showing in dashboard   | ☐       |
| Ads don't affect core theme      | ☐       |

### Future Enhancements
- Add city targeting + per-city slot limits
- Token-based ad credit system
- Auto-renewal or manual renewal
- Expiry email reminders

## Summary

You’re now reusing your plugin’s strong base while tightly integrating with the Houzez theme. You’ve avoided reinventing UI and will ship faster by building with Houzez’s dashboard logic, WooCommerce, and shortcodes.

Stay modular, avoid theme edits, and keep code readable.