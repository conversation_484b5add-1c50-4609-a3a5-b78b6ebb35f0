<?php
/**
 * Template Name: User Dashboard Ad Campaign
 *
 * @package Houzez
 */

if ( !is_user_logged_in() || !houzez_check_role() ) {
    wp_redirect(  home_url() );
}

get_header('dashboard');
?>

<?php get_template_part('template-parts/dashboard/sidebar'); ?>

<div class="dashboard-right">
    <?php get_template_part('template-parts/dashboard/topbar'); ?>

    <div class="dashboard-content">
        <?php
        // Check if user can create campaigns
        if ( ! function_exists( 'houzez_ads_user_can_create_campaigns' ) || ! houzez_ads_user_can_create_campaigns() ) {
            ?>
            <div class="alert alert-warning">
                <h4><?php esc_html_e( 'Access Denied', 'houzez-ads-extension' ); ?></h4>
                <p><?php esc_html_e( 'You do not have permission to access ad campaigns. Please contact the administrator.', 'houzez-ads-extension' ); ?></p>
            </div>
            <?php
        } else {
            // Check what page to display
            $page = isset( $_GET['page'] ) ? sanitize_text_field( $_GET['page'] ) : 'campaigns';

            switch ( $page ) {
                case 'create':
                    ?>
                    <div class="heading d-flex align-items-center justify-content-between">
                        <div class="heading-text">
                            <h2><?php esc_html_e( 'Create Ad Campaign', 'houzez-ads-extension' ); ?></h2>
                        </div>
                        <div>
                            <a href="<?php echo esc_url( remove_query_arg( 'page' ) ); ?>" class="btn btn-secondary">
                                <i class="houzez-icon icon-arrow-left-1 me-2"></i>
                                <?php esc_html_e( 'Back to Campaigns', 'houzez-ads-extension' ); ?>
                            </a>
                        </div>
                    </div>

                    <div class="dashboard-content-block-wrap pt-4">
                        <?php
                        if ( function_exists( 'do_shortcode' ) ) {
                            echo do_shortcode( '[houzez_ads_upload]' );
                        } else {
                            include_once WP_PLUGIN_DIR . '/houzez-ads-extension/frontend/partials/upload-form.php';
                        }
                        ?>
                    </div>
                    <?php
                    break;

                default:
                    ?>
                    <div class="heading d-flex align-items-center justify-content-between">
                        <div class="heading-text">
                            <h2><?php esc_html_e( 'My Ad Campaigns', 'houzez-ads-extension' ); ?></h2>
                        </div>
                        <div>
                            <a href="<?php echo esc_url( add_query_arg( 'page', 'create' ) ); ?>" class="btn btn-primary">
                                <i class="houzez-icon icon-add-circle me-2"></i>
                                <?php esc_html_e( 'Create Campaign', 'houzez-ads-extension' ); ?>
                            </a>
                        </div>
                    </div>

                    <div class="dashboard-content-block-wrap pt-4">
                        <?php
                        if ( function_exists( 'do_shortcode' ) ) {
                            echo do_shortcode( '[houzez_ads_dashboard]' );
                        } else {
                            include_once WP_PLUGIN_DIR . '/houzez-ads-extension/frontend/partials/campaign-dashboard.php';
                        }
                        ?>
                    </div>
                    <?php
                    break;
            }
        }
        ?>
    </div>
</div>

<?php get_footer('dashboard');
