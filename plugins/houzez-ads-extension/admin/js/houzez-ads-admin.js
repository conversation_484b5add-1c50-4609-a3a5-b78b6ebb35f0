/**
 * Admin JavaScript for Houzez Ads Extension
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        // Media uploader for banner images
        var mediaUploader;

        $('.houzez-ads-upload-button').on('click', function(e) {
            e.preventDefault();
            
            var button = $(this);
            var inputField = button.siblings('input[type="url"]');
            var previewContainer = button.siblings('.houzez-ads-image-preview-container');

            // If the media frame already exists, reopen it
            if (mediaUploader) {
                mediaUploader.open();
                return;
            }

            // Create the media frame
            mediaUploader = wp.media({
                title: 'Select Banner Image',
                button: {
                    text: 'Use this image'
                },
                multiple: false
            });

            // When an image is selected, run a callback
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                inputField.val(attachment.url);
                
                // Update preview
                var preview = previewContainer.find('.houzez-ads-image-preview');
                if (preview.length) {
                    preview.attr('src', attachment.url);
                } else {
                    previewContainer.html('<img src="' + attachment.url + '" class="houzez-ads-image-preview" />');
                }
            });

            // Open the media frame
            mediaUploader.open();
        });

        // Dynamic pricing calculation
        function updatePricing() {
            var adZone = $('#houzez_ad_zone').val();
            var duration = $('#houzez_duration').val();
            var adType = $('#houzez_ad_type').val();
            var selectedProperties = $('input[name="houzez_selected_properties[]"]:checked').length;

            if (adZone && duration) {
                var quantity = (adType === 'property' && selectedProperties > 0) ? selectedProperties : 1;

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'houzez_ads_get_pricing',
                        ad_zone: adZone,
                        duration: duration,
                        quantity: quantity,
                        ad_type: adType,
                        nonce: houzez_ads_admin.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#price-text').html('Estimated Price: ' + response.data.formatted_price);
                            $('.houzez-ads-price-display').show();
                        } else {
                            $('#price-text').html('Unable to calculate price');
                            $('.houzez-ads-price-display').show();
                        }
                    },
                    error: function() {
                        console.log('Error calculating price');
                        $('#price-text').html('Error calculating price');
                        $('.houzez-ads-price-display').show();
                    }
                });
            } else {
                $('.houzez-ads-price-display').hide();
            }
        }

        // Update pricing when relevant fields change
        $('#houzez_ad_zone, #houzez_duration, #houzez_ad_type').on('change', updatePricing);
        $('input[name="houzez_selected_properties[]"]').on('change', updatePricing);

        // Show/hide property selection based on ad type
        $('#houzez_ad_type').on('change', function() {
            var adType = $(this).val();
            var propertySelection = $('.houzez-ads-property-selection');
            
            if (adType === 'property') {
                propertySelection.show();
            } else {
                propertySelection.hide();
                $('input[name="houzez_selected_properties[]"]').prop('checked', false);
            }
            
            updatePricing();
        }).trigger('change');

        // Handle custom dates toggle
        $('#use_custom_dates').on('change', function() {
            var $customDates = $('.houzez-ads-custom-dates');
            if ($(this).is(':checked')) {
                $customDates.slideDown();
            } else {
                $customDates.slideUp();
                // Clear the date fields
                $('#houzez_start_date, #houzez_end_date').val('');
            }
        });

        // Zone descriptions
        var zoneDescriptions = {
            'homepage': 'Displays at the top of the homepage - highest visibility area. Recommended size: 1200x300px. Best for major announcements and featured properties.',
            'sidebar': 'Appears in the right sidebar across multiple pages. Recommended size: 300x250px. Great for ongoing promotions and profile ads.',
            'search': 'Shows within property search results. Recommended size: 728x90px. Perfect for property promotions and competitive positioning.',
            'property_detail': 'Displays on individual property pages. Recommended size: 468x60px. Ideal for related properties and services.'
        };

        // Handle zone selection
        $('#houzez_ad_zone').on('change', function() {
            var selectedZone = $(this).val();
            var $description = $('#zone-description');
            var $descriptionText = $('#zone-description-text');

            if (selectedZone && zoneDescriptions[selectedZone]) {
                $descriptionText.html('<strong>Where this ad will appear:</strong><br>' + zoneDescriptions[selectedZone]);
                $description.slideDown();
            } else {
                $description.slideUp();
            }

            // Trigger pricing update
            updatePricing();
        });

        // Show description on page load if zone is already selected
        var initialZone = $('#houzez_ad_zone').val();
        if (initialZone && zoneDescriptions[initialZone]) {
            $('#zone-description-text').html('<strong>Where this ad will appear:</strong><br>' + zoneDescriptions[initialZone]);
            $('#zone-description').show();
        }

        // Initial pricing calculation
        updatePricing();
    });

})(jQuery);
