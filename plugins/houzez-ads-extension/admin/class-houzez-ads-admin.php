<?php
/**
 * The admin-specific functionality of the plugin
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for admin-specific functionality.
 */
class Houzez_Ads_Admin {

	/**
	 * The ID of this plugin.
	 *
	 * @var string $plugin_name The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @var string $version The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @param string $plugin_name The name of this plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;

		// Check and create dashboard page if needed
		add_action( 'admin_init', array( $this, 'ensure_dashboard_page_exists' ) );
	}

	/**
	 * Ensure the dashboard page exists, create if it doesn't.
	 */
	public function ensure_dashboard_page_exists() {
		// Only run this once per session to avoid performance issues
		if ( get_transient( 'houzez_ads_dashboard_check' ) ) {
			return;
		}

		// Set transient to avoid running this repeatedly
		set_transient( 'houzez_ads_dashboard_check', true, HOUR_IN_SECONDS );

		// Check if page exists
		$page_id = get_option( 'houzez_ads_dashboard_page_id' );
		if ( $page_id && get_post( $page_id ) ) {
			return; // Page exists
		}

		// Check if page exists by slug
		$existing_page = get_page_by_path( 'dashboard-ad-campaigns' );
		if ( $existing_page ) {
			update_option( 'houzez_ads_dashboard_page_id', $existing_page->ID );
			return;
		}

		// Create the page
		$this->create_dashboard_page();
	}

	/**
	 * Create the ad campaigns dashboard page.
	 */
	private function create_dashboard_page() {
		$page_data = array(
			'post_title'     => __( 'Ad Campaigns Dashboard', 'houzez-ads-extension' ),
			'post_name'      => 'dashboard-ad-campaigns',
			'post_content'   => __( 'This page is used for the ad campaigns dashboard functionality.', 'houzez-ads-extension' ),
			'post_status'    => 'publish',
			'post_type'      => 'page',
			'post_author'    => 1,
			'comment_status' => 'closed',
			'ping_status'    => 'closed',
		);

		$page_id = wp_insert_post( $page_data );

		if ( $page_id && ! is_wp_error( $page_id ) ) {
			// Set the page template
			update_post_meta( $page_id, '_wp_page_template', 'template/user_dashboard_ad_campaign.php' );

			// Store the page ID for future reference
			update_option( 'houzez_ads_dashboard_page_id', $page_id );
		}
	}

	/**
	 * Register the stylesheets for the admin area.
	 */
	public function enqueue_styles() {
		wp_enqueue_style( 
			$this->plugin_name, 
			HOUZEZ_ADS_EXTENSION_URL . 'admin/css/houzez-ads-admin.css', 
			array(), 
			$this->version, 
			'all' 
		);
	}

	/**
	 * Register the JavaScript for the admin area.
	 */
	public function enqueue_scripts() {
		wp_enqueue_script(
			$this->plugin_name,
			HOUZEZ_ADS_EXTENSION_URL . 'admin/js/houzez-ads-admin.js',
			array( 'jquery' ),
			$this->version,
			false
		);

		// Localize script for AJAX
		wp_localize_script( $this->plugin_name, 'houzez_ads_admin', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce( 'houzez_ads_admin_nonce' )
		));
	}

	/**
	 * Register the campaign post type.
	 */
	public function register_campaign_post_type() {
		$labels = array(
			'name'                  => _x( 'Ad Campaigns', 'Post type general name', 'houzez-ads-extension' ),
			'singular_name'         => _x( 'Ad Campaign', 'Post type singular name', 'houzez-ads-extension' ),
			'menu_name'             => _x( 'Ad Campaigns', 'Admin Menu text', 'houzez-ads-extension' ),
			'name_admin_bar'        => _x( 'Ad Campaign', 'Add New on Toolbar', 'houzez-ads-extension' ),
			'add_new'               => __( 'Add New', 'houzez-ads-extension' ),
			'add_new_item'          => __( 'Add New Campaign', 'houzez-ads-extension' ),
			'new_item'              => __( 'New Campaign', 'houzez-ads-extension' ),
			'edit_item'             => __( 'Edit Campaign', 'houzez-ads-extension' ),
			'view_item'             => __( 'View Campaign', 'houzez-ads-extension' ),
			'all_items'             => __( 'All Campaigns', 'houzez-ads-extension' ),
			'search_items'          => __( 'Search Campaigns', 'houzez-ads-extension' ),
			'parent_item_colon'     => __( 'Parent Campaigns:', 'houzez-ads-extension' ),
			'not_found'             => __( 'No campaigns found.', 'houzez-ads-extension' ),
			'not_found_in_trash'    => __( 'No campaigns found in Trash.', 'houzez-ads-extension' ),
		);

		$args = array(
			'labels'             => $labels,
			'public'             => false,
			'publicly_queryable' => false,
			'show_ui'            => true,
			'show_in_menu'       => true,
			'query_var'          => true,
			'rewrite'            => array( 'slug' => 'ad-campaign' ),
			'capability_type'    => 'post',
			'has_archive'        => false,
			'hierarchical'       => false,
			'menu_position'      => 25,
			'menu_icon'          => 'dashicons-megaphone',
			'supports'           => array( 'title', 'author' ),
			'show_in_rest'       => false,
		);

		register_post_type( 'banner_campaign', $args );
	}

	/**
	 * Add meta boxes for campaign post type.
	 */
	public function add_campaign_meta_boxes() {
		add_meta_box(
			'houzez_campaign_details',
			__( 'Campaign Details', 'houzez-ads-extension' ),
			array( $this, 'campaign_details_meta_box' ),
			'banner_campaign',
			'normal',
			'high'
		);

		add_meta_box(
			'houzez_campaign_banner',
			__( 'Banner Settings', 'houzez-ads-extension' ),
			array( $this, 'campaign_banner_meta_box' ),
			'banner_campaign',
			'normal',
			'high'
		);

		add_meta_box(
			'houzez_campaign_analytics',
			__( 'Campaign Analytics', 'houzez-ads-extension' ),
			array( $this, 'campaign_analytics_meta_box' ),
			'banner_campaign',
			'side',
			'default'
		);
	}

	/**
	 * Campaign details meta box callback.
	 *
	 * @param WP_Post $post Current post object.
	 */
	public function campaign_details_meta_box( $post ) {
		wp_nonce_field( 'houzez_campaign_meta_box', 'houzez_campaign_meta_box_nonce' );

		$campaign = new Houzez_Banner_Campaign( $post );
		$ad_types = houzez_ads_get_available_ad_types();
		$ad_zones = houzez_ads_get_available_zones();
		$durations = houzez_ads_get_available_durations();
		$statuses = houzez_ads_get_campaign_statuses();
		$user_properties = houzez_ads_get_user_properties( $post->post_author );

		include HOUZEZ_ADS_EXTENSION_PATH . 'admin/partials/campaign-details-meta-box.php';
	}

	/**
	 * Campaign banner meta box callback.
	 *
	 * @param WP_Post $post Current post object.
	 */
	public function campaign_banner_meta_box( $post ) {
		$campaign = new Houzez_Banner_Campaign( $post );
		include HOUZEZ_ADS_EXTENSION_PATH . 'admin/partials/campaign-banner-meta-box.php';
	}

	/**
	 * Campaign analytics meta box callback.
	 *
	 * @param WP_Post $post Current post object.
	 */
	public function campaign_analytics_meta_box( $post ) {
		$campaign = new Houzez_Banner_Campaign( $post );
		$analytics = $campaign->get_analytics();
		include HOUZEZ_ADS_EXTENSION_PATH . 'admin/partials/campaign-analytics-meta-box.php';
	}

	/**
	 * Save campaign meta data.
	 *
	 * @param int $post_id Post ID.
	 */
	public function save_campaign_meta( $post_id ) {
		// Prevent infinite loops
		static $saving = false;
		if ( $saving ) {
			return;
		}

		// Check if this is the right post type
		if ( get_post_type( $post_id ) !== 'banner_campaign' ) {
			return;
		}

		// Check if our nonce is set
		if ( ! isset( $_POST['houzez_campaign_meta_box_nonce'] ) ) {
			return;
		}

		// Verify that the nonce is valid
		if ( ! wp_verify_nonce( $_POST['houzez_campaign_meta_box_nonce'], 'houzez_campaign_meta_box' ) ) {
			return;
		}

		// If this is an autosave, our form has not been submitted, so we don't want to do anything
		if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
			return;
		}

		// Check the user's permissions
		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			return;
		}

		// Set saving flag to prevent infinite loops
		$saving = true;

		// Sanitize and save the data
		$data = array();

		if ( isset( $_POST['houzez_ad_type'] ) ) {
			$data['ad_type'] = sanitize_text_field( $_POST['houzez_ad_type'] );
		}

		if ( isset( $_POST['houzez_ad_zone'] ) ) {
			$data['ad_zone'] = sanitize_text_field( $_POST['houzez_ad_zone'] );
		}

		if ( isset( $_POST['houzez_duration'] ) ) {
			$data['duration'] = absint( $_POST['houzez_duration'] );
		}

		if ( isset( $_POST['houzez_selected_properties'] ) ) {
			$data['selected_properties'] = array_map( 'absint', $_POST['houzez_selected_properties'] );
		}

		if ( isset( $_POST['houzez_banner_image'] ) ) {
			$data['banner_image'] = esc_url_raw( $_POST['houzez_banner_image'] );
		}

		if ( isset( $_POST['houzez_banner_link'] ) ) {
			$data['banner_link'] = esc_url_raw( $_POST['houzez_banner_link'] );
		}

		if ( isset( $_POST['houzez_banner_alt'] ) ) {
			$data['banner_alt'] = sanitize_text_field( $_POST['houzez_banner_alt'] );
		}

		if ( isset( $_POST['houzez_campaign_status'] ) ) {
			$data['campaign_status'] = sanitize_text_field( $_POST['houzez_campaign_status'] );
		}

		// Handle custom dates
		if ( isset( $_POST['use_custom_dates'] ) && $_POST['use_custom_dates'] ) {
			if ( isset( $_POST['houzez_start_date'] ) && ! empty( $_POST['houzez_start_date'] ) ) {
				$data['start_date'] = sanitize_text_field( $_POST['houzez_start_date'] );
			}
			if ( isset( $_POST['houzez_end_date'] ) && ! empty( $_POST['houzez_end_date'] ) ) {
				$data['end_date'] = sanitize_text_field( $_POST['houzez_end_date'] );
			}
		}

		// Calculate and save price
		if ( ! empty( $data['ad_zone'] ) && ! empty( $data['duration'] ) ) {
			$quantity = 1;
			if ( isset( $data['selected_properties'] ) && is_array( $data['selected_properties'] ) ) {
				$quantity = count( $data['selected_properties'] );
			}
			$data['price'] = houzez_ads_calculate_campaign_price(
				$data['ad_zone'],
				$data['duration'],
				$quantity,
				$data['ad_type'] ?? 'property'
			);
		}

		// For admin-created campaigns, set default status and skip payment
		if ( current_user_can( 'manage_options' ) && ! isset( $data['campaign_status'] ) ) {
			$data['campaign_status'] = 'approved'; // Auto-approve admin campaigns
		}

		// Save meta data directly to avoid infinite loop
		foreach ( $data as $key => $value ) {
			$meta_key = '_houzez_' . $key;
			if ( $key === 'campaign_status' ) {
				$meta_key = '_houzez_campaign_status';
			} elseif ( $key === 'price' ) {
				$meta_key = '_houzez_campaign_price';
			}
			update_post_meta( $post_id, $meta_key, $value );
		}

		// Reset saving flag
		$saving = false;
	}

	/**
	 * Add custom columns to campaign list table.
	 *
	 * @param array $columns Existing columns.
	 * @return array Modified columns.
	 */
	public function add_campaign_columns( $columns ) {
		$new_columns = array();

		// Keep checkbox and title
		$new_columns['cb'] = $columns['cb'];
		$new_columns['title'] = $columns['title'];

		// Add custom columns
		$new_columns['banner_preview'] = __( 'Banner', 'houzez-ads-extension' );
		$new_columns['ad_type'] = __( 'Type', 'houzez-ads-extension' );
		$new_columns['ad_zone'] = __( 'Zone', 'houzez-ads-extension' );
		$new_columns['duration'] = __( 'Duration', 'houzez-ads-extension' );
		$new_columns['campaign_status'] = __( 'Status', 'houzez-ads-extension' );
		$new_columns['analytics'] = __( 'Analytics', 'houzez-ads-extension' );
		$new_columns['price'] = __( 'Price', 'houzez-ads-extension' );

		// Keep author and date
		$new_columns['author'] = $columns['author'];
		$new_columns['date'] = $columns['date'];

		return $new_columns;
	}

	/**
	 * Display custom column content.
	 *
	 * @param string $column  Column name.
	 * @param int    $post_id Post ID.
	 */
	public function display_campaign_columns( $column, $post_id ) {
		$campaign = new Houzez_Banner_Campaign( $post_id );

		switch ( $column ) {
			case 'banner_preview':
				if ( $campaign->banner_image ) {
					echo '<img src="' . esc_url( $campaign->banner_image ) . '" style="width: 60px; height: 40px; object-fit: cover; border-radius: 4px;" />';
				} else {
					echo '<span class="dashicons dashicons-format-image" style="color: #ddd; font-size: 40px;"></span>';
				}
				break;

			case 'ad_type':
				$ad_types = houzez_ads_get_available_ad_types();
				echo esc_html( $ad_types[ $campaign->ad_type ] ?? $campaign->ad_type );
				break;

			case 'ad_zone':
				$ad_zones = houzez_ads_get_available_zones();
				echo esc_html( $ad_zones[ $campaign->ad_zone ] ?? $campaign->ad_zone );
				break;

			case 'duration':
				if ( $campaign->duration ) {
					printf( _n( '%d day', '%d days', $campaign->duration, 'houzez-ads-extension' ), $campaign->duration );

					if ( $campaign->end_date ) {
						$days_remaining = ceil( ( strtotime( $campaign->end_date ) - current_time( 'timestamp' ) ) / DAY_IN_SECONDS );
						if ( $days_remaining > 0 ) {
							echo '<br><small style="color: #666;">' . sprintf( __( '%d days left', 'houzez-ads-extension' ), $days_remaining ) . '</small>';
						} else {
							echo '<br><small style="color: #d63638;">' . __( 'Expired', 'houzez-ads-extension' ) . '</small>';
						}
					}
				}
				break;

			case 'campaign_status':
				$statuses = houzez_ads_get_campaign_statuses();
				$status = $campaign->campaign_status;
				$status_label = $statuses[ $status ] ?? $status;

				$status_colors = array(
					'pending' => '#f0ad4e',
					'approved' => '#5cb85c',
					'rejected' => '#d9534f',
					'expired' => '#6c757d',
					'paused' => '#17a2b8'
				);

				$color = $status_colors[ $status ] ?? '#6c757d';
				echo '<span style="background: ' . esc_attr( $color ) . '; color: white; padding: 4px 8px; border-radius: 12px; font-size: 11px; font-weight: 600;">' . esc_html( $status_label ) . '</span>';
				break;

			case 'analytics':
				if ( $campaign->campaign_status === 'approved' ) {
					$analytics = $campaign->get_analytics();
					echo '<div style="font-size: 12px;">';
					echo '<div>' . sprintf( __( 'Views: %s', 'houzez-ads-extension' ), number_format( $analytics['impressions'] ) ) . '</div>';
					echo '<div>' . sprintf( __( 'Clicks: %s', 'houzez-ads-extension' ), number_format( $analytics['clicks'] ) ) . '</div>';
					echo '<div>' . sprintf( __( 'CTR: %s%%', 'houzez-ads-extension' ), $analytics['ctr'] ) . '</div>';
					echo '</div>';
				} else {
					echo '<span style="color: #999;">—</span>';
				}
				break;

			case 'price':
				if ( $campaign->price ) {
					echo houzez_ads_format_price( $campaign->price );
				} else {
					echo '<span style="color: #999;">—</span>';
				}
				break;
		}
	}

/**
 * Add sortable columns.
 *
 * @param array $columns Sortable columns.
 * @return array Modified sortable columns.
 */
public function add_sortable_columns( $columns ) {
	$columns['ad_type'] = 'ad_type';
	$columns['ad_zone'] = 'ad_zone';
	$columns['duration'] = 'duration';
	$columns['campaign_status'] = 'campaign_status';
	$columns['price'] = 'price';

	return $columns;
}

/**
 * Add campaign filters to admin list.
 */
public function add_campaign_filters() {
	global $typenow;

	if ( $typenow !== 'banner_campaign' ) {
		return;
	}

	// Status filter
	$statuses = houzez_ads_get_campaign_statuses();
	$current_status = $_GET['campaign_status'] ?? '';

	echo '<select name="campaign_status">';
	echo '<option value="">' . __( 'All Statuses', 'houzez-ads-extension' ) . '</option>';
	foreach ( $statuses as $key => $label ) {
		$selected = selected( $current_status, $key, false );
		echo '<option value="' . esc_attr( $key ) . '"' . $selected . '>' . esc_html( $label ) . '</option>';
	}
	echo '</select>';

	// Zone filter
	$zones = houzez_ads_get_available_zones();
	$current_zone = $_GET['ad_zone'] ?? '';

	echo '<select name="ad_zone">';
	echo '<option value="">' . __( 'All Zones', 'houzez-ads-extension' ) . '</option>';
	foreach ( $zones as $key => $label ) {
		$selected = selected( $current_zone, $key, false );
		echo '<option value="' . esc_attr( $key ) . '"' . $selected . '>' . esc_html( $label ) . '</option>';
	}
	echo '</select>';

	// Type filter
	$types = houzez_ads_get_available_ad_types();
	$current_type = $_GET['ad_type'] ?? '';

	echo '<select name="ad_type">';
	echo '<option value="">' . __( 'All Types', 'houzez-ads-extension' ) . '</option>';
	foreach ( $types as $key => $label ) {
		$selected = selected( $current_type, $key, false );
		echo '<option value="' . esc_attr( $key ) . '"' . $selected . '>' . esc_html( $label ) . '</option>';
	}
	echo '</select>';
}

/**
 * Filter campaigns by meta values.
 *
 * @param WP_Query $query The WP_Query object.
 */
public function filter_campaigns_by_meta( $query ) {
	global $pagenow, $typenow;

	if ( $pagenow !== 'edit.php' || $typenow !== 'banner_campaign' || ! is_admin() ) {
		return;
	}

	$meta_query = array();

	// Filter by status
	if ( ! empty( $_GET['campaign_status'] ) ) {
		$meta_query[] = array(
			'key' => '_houzez_campaign_status',
			'value' => sanitize_text_field( $_GET['campaign_status'] ),
			'compare' => '='
		);
	}

	// Filter by zone
	if ( ! empty( $_GET['ad_zone'] ) ) {
		$meta_query[] = array(
			'key' => '_houzez_ad_zone',
			'value' => sanitize_text_field( $_GET['ad_zone'] ),
			'compare' => '='
		);
	}

	// Filter by type
	if ( ! empty( $_GET['ad_type'] ) ) {
		$meta_query[] = array(
			'key' => '_houzez_ad_type',
			'value' => sanitize_text_field( $_GET['ad_type'] ),
			'compare' => '='
		);
	}

	if ( ! empty( $meta_query ) ) {
		$query->set( 'meta_query', $meta_query );
	}

	// Handle sorting
	$orderby = $query->get( 'orderby' );

	if ( in_array( $orderby, array( 'ad_type', 'ad_zone', 'duration', 'campaign_status', 'price' ) ) ) {
		$meta_key = '_houzez_' . $orderby;
		if ( $orderby === 'campaign_status' ) {
			$meta_key = '_houzez_campaign_status';
		} elseif ( $orderby === 'price' ) {
			$meta_key = '_houzez_campaign_price';
		}

		$query->set( 'meta_key', $meta_key );
		$query->set( 'orderby', 'meta_value' );
	}
}

/**
 * Add bulk actions.
 *
 * @param array $actions Existing bulk actions.
 * @return array Modified bulk actions.
 */
public function add_bulk_actions( $actions ) {
	$actions['approve_campaigns'] = __( 'Approve Campaigns', 'houzez-ads-extension' );
	$actions['reject_campaigns'] = __( 'Reject Campaigns', 'houzez-ads-extension' );
	$actions['pause_campaigns'] = __( 'Pause Campaigns', 'houzez-ads-extension' );

	return $actions;
}

/**
 * Handle bulk actions.
 *
 * @param string $redirect_to Redirect URL.
 * @param string $doaction    Action name.
 * @param array  $post_ids    Post IDs.
 * @return string Modified redirect URL.
 */
public function handle_bulk_actions( $redirect_to, $doaction, $post_ids ) {
	if ( ! in_array( $doaction, array( 'approve_campaigns', 'reject_campaigns', 'pause_campaigns' ) ) ) {
		return $redirect_to;
	}

	$processed = 0;

	foreach ( $post_ids as $post_id ) {
		$campaign = new Houzez_Banner_Campaign( $post_id );

		switch ( $doaction ) {
			case 'approve_campaigns':
				$campaign->approve();
				$processed++;
				break;

			case 'reject_campaigns':
				$campaign->reject();
				$processed++;
				break;

			case 'pause_campaigns':
				$campaign->save( array( 'campaign_status' => 'paused' ) );
				$processed++;
				break;
		}
	}

	$redirect_to = add_query_arg( array(
		'bulk_action' => $doaction,
		'processed' => $processed
	), $redirect_to );

	return $redirect_to;
}

/**
 * Handle get pricing AJAX request for admin.
 */
public function handle_get_pricing() {
	check_ajax_referer( 'houzez_ads_admin_nonce', 'nonce' );

	$ad_zone = sanitize_text_field( $_POST['ad_zone'] ?? '' );
	$duration = absint( $_POST['duration'] ?? 0 );
	$quantity = absint( $_POST['quantity'] ?? 1 );
	$ad_type = sanitize_text_field( $_POST['ad_type'] ?? 'property' );

	if ( $ad_zone && $duration ) {
		$price = houzez_ads_calculate_campaign_price( $ad_zone, $duration, $quantity, $ad_type );
		wp_send_json_success( array(
			'price' => $price,
			'formatted_price' => houzez_ads_format_price( $price ),
			'debug' => array(
				'zone' => $ad_zone,
				'duration' => $duration,
				'quantity' => $quantity,
				'type' => $ad_type
			)
		) );
	} else {
		wp_send_json_error( array(
			'message' => 'Invalid parameters',
			'debug' => array(
				'zone' => $ad_zone,
				'duration' => $duration,
				'quantity' => $quantity,
				'type' => $ad_type
			)
		) );
	}
}
}
