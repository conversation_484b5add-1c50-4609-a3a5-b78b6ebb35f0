/**
 * Admin styles for Houzez Ads Extension
 */

.houzez-ads-meta-box {
    padding: 15px;
}

.houzez-ads-field {
    margin-bottom: 15px;
}

.houzez-ads-field label {
    display: block;
    font-weight: 600;
    margin-bottom: 5px;
}

.houzez-ads-field input,
.houzez-ads-field select,
.houzez-ads-field textarea {
    width: 100%;
    max-width: 400px;
}

.houzez-ads-field textarea {
    height: 80px;
}

.houzez-ads-properties-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    background: #f9f9f9;
}

.houzez-ads-property-item {
    margin-bottom: 8px;
}

.houzez-ads-property-item label {
    font-weight: normal;
    margin-left: 8px;
}

.houzez-ads-analytics {
    text-align: center;
}

.houzez-ads-stat {
    margin-bottom: 15px;
    padding: 10px;
    background: #f1f1f1;
    border-radius: 4px;
}

.houzez-ads-stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
    display: block;
}

.houzez-ads-stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

.houzez-ads-image-preview {
    max-width: 300px;
    margin-top: 10px;
    border: 1px solid #ddd;
    padding: 5px;
}

.houzez-ads-upload-button {
    margin-top: 5px;
}

.houzez-ads-price-display {
    font-size: 18px;
    font-weight: bold;
    color: #0073aa;
    margin-top: 10px;
    padding: 10px;
    background: #f0f8ff;
    border: 1px solid #0073aa;
    border-radius: 4px;
}

.houzez-ads-zone-description {
    margin-top: 10px;
    padding: 12px;
    background: #f9f9f9;
    border-left: 4px solid #0073aa;
    border-radius: 0 4px 4px 0;
    font-size: 13px;
    line-height: 1.5;
}

.houzez-ads-zone-description p {
    margin: 0;
    color: #555;
}

.houzez-ads-zone-description strong {
    color: #0073aa;
    display: block;
    margin-bottom: 5px;
}
