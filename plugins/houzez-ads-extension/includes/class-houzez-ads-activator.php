<?php
/**
 * Fired during plugin activation
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/includes
 */

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 */
class Houzez_Ads_Activator {

	/**
	 * Short Description. (use period)
	 *
	 * Long Description.
	 */
	public static function activate() {
		// Create database tables if needed
		self::create_database_tables();
		
		// Set default options
		self::set_default_options();
		
		// Flush rewrite rules
		flush_rewrite_rules();
	}

	/**
	 * Create custom database tables for analytics and campaign data.
	 */
	private static function create_database_tables() {
		global $wpdb;

		$charset_collate = $wpdb->get_charset_collate();

		// Analytics table for tracking impressions and clicks
		$analytics_table = $wpdb->prefix . 'houzez_ad_analytics';
		$analytics_sql = "CREATE TABLE $analytics_table (
			id mediumint(9) NOT NULL AUTO_INCREMENT,
			campaign_id bigint(20) NOT NULL,
			event_type varchar(20) NOT NULL,
			user_id bigint(20) DEFAULT NULL,
			ip_address varchar(45) DEFAULT NULL,
			user_agent text DEFAULT NULL,
			referrer text DEFAULT NULL,
			created_at datetime DEFAULT CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			KEY campaign_id (campaign_id),
			KEY event_type (event_type),
			KEY created_at (created_at)
		) $charset_collate;";

		require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
		dbDelta( $analytics_sql );
	}

	/**
	 * Set default plugin options.
	 */
	private static function set_default_options() {
		// Default pricing options
		$default_pricing = array(
			'homepage' => array(
				'7' => 49,
				'14' => 89,
				'30' => 149
			),
			'sidebar' => array(
				'7' => 29,
				'14' => 49,
				'30' => 89
			),
			'search' => array(
				'7' => 10,
				'14' => 18,
				'30' => 30
			),
			'property_detail' => array(
				'7' => 35,
				'14' => 59,
				'30' => 99
			)
		);

		add_option( 'houzez_ads_pricing', $default_pricing );
		add_option( 'houzez_ads_auto_approval', false );
		add_option( 'houzez_ads_rotation_interval', 30 ); // minutes
		add_option( 'houzez_ads_currency_symbol', '$' );
		add_option( 'houzez_ads_currency_position', 'before' );

		// Create dashboard page if it doesn't exist
		self::create_dashboard_page();
	}

	/**
	 * Create the ad campaigns dashboard page.
	 */
	private static function create_dashboard_page() {
		// Check if page already exists
		$existing_page = get_page_by_path( 'dashboard-ad-campaigns' );
		if ( $existing_page ) {
			return;
		}

		// Create the page
		$page_data = array(
			'post_title'     => __( 'Ad Campaigns Dashboard', 'houzez-ads-extension' ),
			'post_name'      => 'dashboard-ad-campaigns',
			'post_content'   => __( 'This page is used for the ad campaigns dashboard functionality.', 'houzez-ads-extension' ),
			'post_status'    => 'publish',
			'post_type'      => 'page',
			'post_author'    => 1,
			'comment_status' => 'closed',
			'ping_status'    => 'closed',
		);

		$page_id = wp_insert_post( $page_data );

		if ( $page_id && ! is_wp_error( $page_id ) ) {
			// Set the page template
			update_post_meta( $page_id, '_wp_page_template', 'template/user_dashboard_ad_campaign.php' );

			// Store the page ID for future reference
			update_option( 'houzez_ads_dashboard_page_id', $page_id );
		}
	}
}
