<?php
/**
 * Plugin Name: <PERSON><PERSON>z Ads Extension
 * Plugin URI:  https://example.com/
 * Description: Extends the Real Estate Banner Ads Manager for Ho<PERSON><PERSON> theme with targeted ad campaigns, WooCommerce integration, and Houzez dashboard UI.
 * Version:     1.0.0
 * Author:      Your Name
 * Author URI:  https://example.com/
 * License:     GPL-2.0+
 * License URI: http://www.gnu.org/licenses/gpl-2.0.txt
 * Text Domain: houzez-ads-extension
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 7.0
 * WC tested up to: 9.9
 * Requires Plugins: woocommerce
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Currently plugin version.
 */
define( 'HOUZEZ_ADS_EXTENSION_VERSION', '1.0.0' );

/**
 * Plugin directory path.
 */
define( 'HOUZEZ_ADS_EXTENSION_PATH', plugin_dir_path( __FILE__ ) );

/**
 * Plugin directory URL.
 */
define( 'HOUZEZ_ADS_EXTENSION_URL', plugin_dir_url( __FILE__ ) );

/**
 * Plugin basename.
 */
define( 'HOUZEZ_ADS_EXTENSION_BASENAME', plugin_basename( __FILE__ ) );

/**
 * The code that runs during plugin activation.
 */
function activate_houzez_ads_extension() {
	require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-ads-activator.php';
	Houzez_Ads_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 */
function deactivate_houzez_ads_extension() {
	require_once HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-ads-deactivator.php';
	Houzez_Ads_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_houzez_ads_extension' );
register_deactivation_hook( __FILE__, 'deactivate_houzez_ads_extension' );

/**
 * Declare WooCommerce HPOS compatibility.
 */
add_action( 'before_woocommerce_init', function() {
	if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
		\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', __FILE__, true );
	}
} );

/**
 * Check for dependencies.
 */
add_action( 'admin_notices', function() {
	// Check for WooCommerce
	if ( ! class_exists( 'WooCommerce' ) ) {
		?>
		<div class="notice notice-warning is-dismissible">
			<p>
				<strong><?php _e( 'Houzez Ads Extension', 'houzez-ads-extension' ); ?></strong>:
				<?php _e( 'WooCommerce is required for the e-commerce functionality. Some features may not work without it.', 'houzez-ads-extension' ); ?>
			</p>
		</div>
		<?php
	}

	// Check for Houzez theme
	$theme = wp_get_theme();
	if ( $theme->get( 'Name' ) !== 'Houzez' && $theme->get( 'Template' ) !== 'houzez' ) {
		?>
		<div class="notice notice-info is-dismissible">
			<p>
				<strong><?php _e( 'Houzez Ads Extension', 'houzez-ads-extension' ); ?></strong>:
				<?php _e( 'This plugin is designed for the Houzez theme. Some features may not work properly with other themes.', 'houzez-ads-extension' ); ?>
			</p>
		</div>
		<?php
	}
} );

/**
 * Registers the `banner_campaign` custom post type.
 */
// Commented out - function is defined in admin class
// function houzez_ads_register_campaign_post_type() {
    $labels = array(
        'name'                  => _x( 'Campaigns', 'Post type general name', 'houzez-ads-extension' ),
        'singular_name'         => _x( 'Campaign', 'Post type singular name', 'houzez-ads-extension' ),
        'menu_name'             => _x( 'Ad Campaigns', 'Admin Menu text', 'houzez-ads-extension' ),
        'name_admin_bar'        => _x( 'Campaign', 'Add New on Toolbar', 'houzez-ads-extension' ),
        'add_new'               => __( 'Add New', 'houzez-ads-extension' ),
        'add_new_item'          => __( 'Add New Campaign', 'houzez-ads-extension' ),
        'new_item'              => __( 'New Campaign', 'houzez-ads-extension' ),
        'edit_item'             => __( 'Edit Campaign', 'houzez-ads-extension' ),
        'view_item'             => __( 'View Campaign', 'houzez-ads-extension' ),
        'all_items'             => __( 'All Campaigns', 'houzez-ads-extension' ),
        'search_items'          => __( 'Search Campaigns', 'houzez-ads-extension' ),
        'parent_item_colon'     => __( 'Parent Campaigns:', 'houzez-ads-extension' ),
        'not_found'             => __( 'No campaigns found.', 'houzez-ads-extension' ),
        'not_found_in_trash'    => __( 'No campaigns found in Trash.', 'houzez-ads-extension' ),
        'featured_image'        => _x( 'Campaign Cover Image', 'Overrides the “Featured Image” phrase for this post type. Added in 4.3', 'houzez-ads-extension' ),
        'set_featured_image'    => _x( 'Set cover image', 'Overrides the “Set featured image” phrase for this post type. Added in 4.3', 'houzez-ads-extension' ),
        'remove_featured_image' => _x( 'Remove cover image', 'Overrides the “Remove featured image” phrase for this post type. Added in 4.3', 'houzez-ads-extension' ),
        'use_featured_image'    => _x( 'Use as cover image', 'Overrides the “Use as featured image” phrase for this post type. Added in 4.3', 'houzez-ads-extension' ),
        'archives'              => _x( 'Campaign archives', 'The post type archive label used in nav menus. Default “Post Archives”. Added in 4.4', 'houzez-ads-extension' ),
        'insert_into_item'      => _x( 'Insert into campaign', 'Overrides the “Insert into post”/”Insert into page” phrase (used when inserting media into a post). Added in 4.4', 'houzez-ads-extension' ),
        'uploaded_to_this_item' => _x( 'Uploaded to this campaign', 'Overrides the “Uploaded to this post”/”Uploaded to this page” phrase (used when viewing media attached to a post). Added in 4.4', 'houzez-ads-extension' ),
        'filter_items_list'     => _x( 'Filter campaigns list', 'Screen reader text for the filter links heading on the post type listing screen. Default “Filter posts list”/”Filter pages list”. Added in 4.4', 'houzez-ads-extension' ),
        'items_list_navigation' => _x( 'Campaigns list navigation', 'Screen reader text for the pagination heading on the post type listing screen. Default “Posts list navigation”/”Pages list navigation”. Added in 4.4', 'houzez-ads-extension' ),
        'items_list'            => _x( 'Campaigns list', 'Screen reader text for the items list heading on the post type listing screen. Default “Posts list”/”Pages list”. Added in 4.4', 'houzez-ads-extension' ),
    );

    $args = array(
        'labels'             => $labels,
        'public'             => true,
        'publicly_queryable' => true,
        'show_ui'            => true,
        'show_in_menu'       => true,
        'query_var'          => true,
        'rewrite'            => array( 'slug' => 'ad-campaign' ),
        'capability_type'    => 'post',
        'has_archive'        => true,
        'hierarchical'       => false,
        'menu_position'      => null,
        'supports'           => array( 'title', 'editor', 'author', 'thumbnail' ),
        'show_in_rest'       => true,
    );

    // register_post_type( 'banner_campaign', $args );
// }
// add_action( 'init', 'houzez_ads_register_campaign_post_type' );

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require HOUZEZ_ADS_EXTENSION_PATH . 'includes/class-houzez-ads-extension.php';

/**
 * Begins execution of the plugin.
 */
function run_houzez_ads_extension() {
	$plugin = new Houzez_Ads_Extension();
	$plugin->run();
}
run_houzez_ads_extension();
