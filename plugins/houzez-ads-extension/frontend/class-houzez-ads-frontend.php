<?php
/**
 * The public-facing functionality of the plugin
 *
 * @package HouzezAdsExtension
 * @subpackage HouzezAdsExtension/frontend
 */

/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for public-facing functionality.
 */
class Houzez_Ads_Frontend {

	/**
	 * The ID of this plugin.
	 *
	 * @var string $plugin_name The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @var string $version The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @param string $plugin_name The name of the plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;

		// Add dashboard integration hooks
		add_action( 'init', array( $this, 'add_dashboard_integration' ) );
	}

	/**
	 * Register the stylesheets for the public-facing side of the site.
	 */
	public function enqueue_styles() {
		wp_enqueue_style( 
			$this->plugin_name, 
			HOUZEZ_ADS_EXTENSION_URL . 'frontend/css/houzez-ads-frontend.css', 
			array(), 
			$this->version, 
			'all' 
		);
	}

	/**
	 * Register the JavaScript for the public-facing side of the site.
	 */
	public function enqueue_scripts() {
		wp_enqueue_script( 
			$this->plugin_name, 
			HOUZEZ_ADS_EXTENSION_URL . 'frontend/js/houzez-ads-frontend.js', 
			array( 'jquery' ), 
			$this->version, 
			false 
		);

		// Localize script for AJAX tracking
		wp_localize_script( $this->plugin_name, 'houzez_ads_ajax', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce( 'houzez_ads_tracking_nonce' )
		));
	}

	/**
	 * Register shortcodes and frontend hooks.
	 */
	public function register_shortcodes() {
		add_shortcode( 'houzez_ads_zone', array( $this, 'display_ad_zone' ) );
		add_shortcode( 'houzez_ads_dashboard', array( $this, 'display_campaign_dashboard' ) );
		add_shortcode( 'houzez_ads_upload', array( $this, 'display_upload_form' ) );

		// Register AJAX handlers
		add_action( 'wp_ajax_houzez_ads_track_click', array( $this, 'handle_click_tracking' ) );
		add_action( 'wp_ajax_nopriv_houzez_ads_track_click', array( $this, 'handle_click_tracking' ) );
		add_action( 'wp_ajax_houzez_ads_get_pricing', array( $this, 'handle_get_pricing' ) );
		add_action( 'wp_ajax_nopriv_houzez_ads_get_pricing', array( $this, 'handle_get_pricing' ) );

		// Frontend display hooks
		add_action( 'pre_get_posts', 'houzez_ads_insert_promoted_properties' );
		add_filter( 'the_posts', 'houzez_ads_inject_sponsored_properties', 10, 2 );

		// Houzez theme integration hooks
		add_action( 'houzez_before_property_loop', array( $this, 'display_search_zone_ads' ) );
		add_action( 'houzez_after_property_item', array( $this, 'display_property_detail_ads' ), 10, 2 );
		add_action( 'houzez_sidebar', array( $this, 'display_sidebar_ads' ) );
		add_action( 'wp_head', array( $this, 'add_sponsored_property_styles' ) );

		// Handle click tracking redirects
		add_action( 'template_redirect', array( $this, 'handle_click_redirect' ) );
	}

	/**
	 * Display ad zone shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string Shortcode output.
	 */
	public function display_ad_zone( $atts ) {
		$atts = shortcode_atts( array(
			'zone' => 'homepage',
			'class' => '',
		), $atts, 'houzez_ads_zone' );

		$campaign = houzez_ads_get_rotated_campaign( $atts['zone'] );
		
		if ( ! $campaign ) {
			return '';
		}

		$banner_campaign = new Houzez_Banner_Campaign( $campaign );
		$html = $banner_campaign->get_display_html();

		if ( $atts['class'] ) {
			$html = str_replace( 'class="houzez-ad-campaign"', 'class="houzez-ad-campaign ' . esc_attr( $atts['class'] ) . '"', $html );
		}

		return $html;
	}

	/**
	 * Display campaign dashboard shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string Shortcode output.
	 */
	public function display_campaign_dashboard( $atts ) {
		if ( ! is_user_logged_in() ) {
			return '<p>' . __( 'Please log in to view your campaigns.', 'houzez-ads-extension' ) . '</p>';
		}

		if ( ! houzez_ads_user_can_create_campaigns() ) {
			return '<p>' . __( 'You do not have permission to create campaigns.', 'houzez-ads-extension' ) . '</p>';
		}

		ob_start();
		include HOUZEZ_ADS_EXTENSION_PATH . 'frontend/partials/campaign-dashboard.php';
		return ob_get_clean();
	}

	/**
	 * Display upload form shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string Shortcode output.
	 */
	public function display_upload_form( $atts ) {
		if ( ! is_user_logged_in() ) {
			return '<p>' . __( 'Please log in to create a campaign.', 'houzez-ads-extension' ) . '</p>';
		}

		if ( ! houzez_ads_user_can_create_campaigns() ) {
			return '<p>' . __( 'You do not have permission to create campaigns.', 'houzez-ads-extension' ) . '</p>';
		}

		ob_start();
		include HOUZEZ_ADS_EXTENSION_PATH . 'frontend/partials/upload-form.php';
		return ob_get_clean();
	}

	/**
	 * Handle click tracking AJAX request.
	 */
	public function handle_click_tracking() {
		check_ajax_referer( 'houzez_ads_tracking_nonce', 'nonce' );

		$campaign_id = absint( $_POST['campaign_id'] ?? 0 );
		
		if ( $campaign_id ) {
			houzez_ads_track_click( $campaign_id );
		}

		wp_die();
	}

	/**
	 * Handle get pricing AJAX request.
	 */
	public function handle_get_pricing() {
		check_ajax_referer( 'houzez_ads_tracking_nonce', 'nonce' );

		$ad_zone = sanitize_text_field( $_POST['ad_zone'] ?? '' );
		$duration = absint( $_POST['duration'] ?? 0 );
		$quantity = absint( $_POST['quantity'] ?? 1 );
		$ad_type = sanitize_text_field( $_POST['ad_type'] ?? 'property' );

		if ( $ad_zone && $duration ) {
			$price = houzez_ads_calculate_campaign_price( $ad_zone, $duration, $quantity, $ad_type );
			wp_send_json_success( array(
				'price' => $price,
				'formatted_price' => houzez_ads_format_price( $price )
			) );
		} else {
			wp_send_json_error( array( 'message' => 'Invalid parameters' ) );
		}
	}

	/**
	 * Display search zone ads.
	 */
	public function display_search_zone_ads() {
		if ( is_search() || is_post_type_archive( 'property' ) ) {
			echo do_shortcode( '[houzez_ads_zone zone="search" class="houzez-ad-search"]' );
		}
	}

	/**
	 * Display property detail ads.
	 *
	 * @param int   $index Property index in loop.
	 * @param array $property Property data.
	 */
	public function display_property_detail_ads( $index, $property ) {
		// Display ad every 5th property
		if ( ( $index + 1 ) % 5 === 0 ) {
			echo do_shortcode( '[houzez_ads_zone zone="property_detail" class="houzez-ad-property-detail"]' );
		}
	}

	/**
	 * Display sidebar ads.
	 */
	public function display_sidebar_ads() {
		echo do_shortcode( '[houzez_ads_zone zone="sidebar" class="houzez-ad-sidebar"]' );
	}

	/**
	 * Add sponsored property styles to head.
	 */
	public function add_sponsored_property_styles() {
		?>
		<style>
		.property-item.sponsored {
			position: relative;
			border: 2px solid var(--houzez-primary, #f39c12) !important;
		}
		.property-item.sponsored::before {
			content: "<?php _e( 'Sponsored', 'houzez-ads-extension' ); ?>";
			position: absolute;
			top: 10px;
			right: 10px;
			background: var(--houzez-primary, #f39c12);
			color: #fff;
			padding: 4px 8px;
			border-radius: 4px;
			font-size: 11px;
			font-weight: 600;
			z-index: 10;
			text-transform: uppercase;
		}
		</style>
		<?php
	}

	/**
	 * Handle click tracking redirects.
	 */
	public function handle_click_redirect() {
		if ( isset( $_GET['houzez_ads_track'] ) && $_GET['houzez_ads_track'] === 'click' ) {
			$campaign_id = absint( $_GET['campaign_id'] ?? 0 );
			$redirect_url = urldecode( $_GET['redirect'] ?? '' );

			if ( $campaign_id && $redirect_url ) {
				// Track the click
				houzez_ads_track_click( $campaign_id );

				// Redirect to target URL
				wp_redirect( $redirect_url );
				exit;
			}
		}
	}

	/**
	 * Add dashboard integration using WordPress hooks instead of modifying theme files.
	 */
	public function add_dashboard_integration() {
		// Add rewrite rule for virtual dashboard page
		add_rewrite_rule( '^ad-campaigns-dashboard/?$', 'index.php?ad_campaigns_dashboard=1', 'top' );
		add_filter( 'query_vars', array( $this, 'add_query_vars' ) );
		add_action( 'template_redirect', array( $this, 'handle_dashboard_page' ) );

		// Add dashboard menu items using JavaScript injection
		add_action( 'wp_footer', array( $this, 'add_dashboard_menu_script' ) );
	}

	/**
	 * Add custom query vars.
	 */
	public function add_query_vars( $vars ) {
		$vars[] = 'ad_campaigns_dashboard';
		return $vars;
	}

	/**
	 * Add JavaScript to inject menu items into Houzez dashboard.
	 */
	public function add_dashboard_menu_script() {
		// Only add on dashboard pages for logged-in users
		if ( ! is_user_logged_in() || ! function_exists( 'houzez_check_role' ) || ! houzez_check_role() ) {
			return;
		}

		// Check if user can create campaigns
		if ( ! function_exists( 'houzez_ads_user_can_create_campaigns' ) || ! houzez_ads_user_can_create_campaigns() ) {
			return;
		}

		// Only add on Houzez dashboard pages
		global $post;
		if ( ! $post || ! is_page() ) {
			return;
		}

		$template = get_page_template_slug( $post->ID );
		if ( strpos( $template, 'user_dashboard' ) === false ) {
			return;
		}

		$dashboard_url = home_url( '/ad-campaigns-dashboard/' );
		?>
		<script type="text/javascript">
		jQuery(document).ready(function($) {
			// Find the dashboard menu and add our items
			var $lastNavBox = $('.dashboard-sidebar .nav-box').last();
			if ($lastNavBox.length) {
				var adCampaignsMenu = '<div class="nav-box">' +
					'<h5>Advertising</h5>' +
					'<ul>' +
						'<li><a href="<?php echo esc_js( $dashboard_url ); ?>">' +
							'<i class="houzez-icon icon-megaphone"></i>' +
							'<span>My Campaigns</span>' +
						'</a></li>' +
						'<li><a href="<?php echo esc_js( add_query_arg( 'action', 'create', $dashboard_url ) ); ?>">' +
							'<i class="houzez-icon icon-add-circle"></i>' +
							'<span>Create Campaign</span>' +
						'</a></li>' +
					'</ul>' +
				'</div>';
				$lastNavBox.after(adCampaignsMenu);
			}
		});
		</script>
		<?php
	}

	/**
	 * Handle the virtual dashboard page.
	 */
	public function handle_dashboard_page() {
		if ( ! get_query_var( 'ad_campaigns_dashboard' ) ) {
			return;
		}

		// Check permissions
		if ( ! is_user_logged_in() || ! function_exists( 'houzez_check_role' ) || ! houzez_check_role() ) {
			wp_redirect( home_url() );
			exit;
		}

		if ( ! function_exists( 'houzez_ads_user_can_create_campaigns' ) || ! houzez_ads_user_can_create_campaigns() ) {
			wp_die( __( 'You do not have permission to access ad campaigns.', 'houzez-ads-extension' ) );
		}

		// Load the dashboard template
		$this->load_dashboard_template();
		exit;
	}

	/**
	 * Load the dashboard template.
	 */
	private function load_dashboard_template() {
		$action = isset( $_GET['action'] ) ? sanitize_text_field( $_GET['action'] ) : 'list';

		// Simple standalone page
		?>
		<!DOCTYPE html>
		<html <?php language_attributes(); ?>>
		<head>
			<meta charset="<?php bloginfo( 'charset' ); ?>">
			<meta name="viewport" content="width=device-width, initial-scale=1">
			<title><?php esc_html_e( 'Ad Campaigns Dashboard', 'houzez-ads-extension' ); ?> - <?php bloginfo( 'name' ); ?></title>
			<?php wp_head(); ?>
			<style>
				body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
				.container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
				.header { border-bottom: 1px solid #eee; padding-bottom: 20px; margin-bottom: 30px; }
				.btn { display: inline-block; padding: 10px 20px; background: #007cba; color: white; text-decoration: none; border-radius: 4px; }
				.btn:hover { background: #005a87; color: white; }
				.btn-secondary { background: #6c757d; }
				.btn-secondary:hover { background: #545b62; }
			</style>
		</head>
		<body>
			<div class="container">
				<?php if ( $action === 'create' ): ?>
					<div class="header">
						<h1><?php esc_html_e( 'Create Ad Campaign', 'houzez-ads-extension' ); ?></h1>
						<a href="<?php echo esc_url( remove_query_arg( 'action' ) ); ?>" class="btn btn-secondary">
							<?php esc_html_e( 'Back to Campaigns', 'houzez-ads-extension' ); ?>
						</a>
					</div>
					<div class="content">
						<?php echo do_shortcode( '[houzez_ads_upload]' ); ?>
					</div>
				<?php else: ?>
					<div class="header">
						<h1><?php esc_html_e( 'My Ad Campaigns', 'houzez-ads-extension' ); ?></h1>
						<a href="<?php echo esc_url( add_query_arg( 'action', 'create' ) ); ?>" class="btn">
							<?php esc_html_e( 'Create Campaign', 'houzez-ads-extension' ); ?>
						</a>
					</div>
					<div class="content">
						<?php echo do_shortcode( '[houzez_ads_dashboard]' ); ?>
					</div>
				<?php endif; ?>
			</div>
			<?php wp_footer(); ?>
		</body>
		</html>
		<?php
	}
}
