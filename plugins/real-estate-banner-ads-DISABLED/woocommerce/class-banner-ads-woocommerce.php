<?php
/**
 * WooCommerce integration functionality
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/woocommerce
 */

/**
 * WooCommerce integration class.
 *
 * Handles integration with WooCommerce for banner ad purchases and order processing.
 */
class Banner_Ads_WooCommerce {

	/**
	 * The ID of this plugin.
	 *
	 * @var string $plugin_name The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @var string $version The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @param string $plugin_name The name of the plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;
	}

	/**
	 * Create banner products if they don't exist.
	 */
	public function create_banner_products() {
		// This will be called on init, but we only want to run it once
		if ( get_option( 'banner_ads_products_created' ) ) {
			return;
		}

		$this->create_default_banner_products();
		update_option( 'banner_ads_products_created', true );
	}

	/**
	 * Create default banner products.
	 */
	private function create_default_banner_products() {
		if ( ! class_exists( 'WooCommerce' ) ) {
			return;
		}

		$packages = array(
			array(
				'name' => 'Banner Ad - 1 Week',
				'price' => 99.00,
				'duration' => 7,
				'sku' => 'banner-1-week',
				'description' => 'Display your banner ad for 1 week in your chosen zone.'
			),
			array(
				'name' => 'Banner Ad - 1 Month',
				'price' => 299.00,
				'duration' => 30,
				'sku' => 'banner-1-month',
				'description' => 'Display your banner ad for 1 month in your chosen zone.'
			),
			array(
				'name' => 'Banner Ad - 3 Months',
				'price' => 799.00,
				'duration' => 90,
				'sku' => 'banner-3-months',
				'description' => 'Display your banner ad for 3 months in your chosen zone.'
			)
		);

		foreach ( $packages as $package ) {
			// Check if product already exists
			$existing_product = wc_get_product_id_by_sku( $package['sku'] );
			if ( $existing_product ) {
				continue;
			}

			$product = new WC_Product_Simple();
			$product->set_name( $package['name'] );
			$product->set_status( 'publish' );
			$product->set_catalog_visibility( 'visible' );
			$product->set_description( $package['description'] );
			$product->set_short_description( $package['description'] );
			$product->set_sku( $package['sku'] );
			$product->set_price( $package['price'] );
			$product->set_regular_price( $package['price'] );
			$product->set_manage_stock( false );
			$product->set_stock_status( 'instock' );
			$product->set_virtual( true );
			$product->set_downloadable( false );
			
			// Add custom meta for banner duration
			$product->update_meta_data( '_banner_duration_days', $package['duration'] );
			$product->update_meta_data( '_is_banner_product', 'yes' );
			
			$product->save();
		}
	}

	/**
	 * Process banner order when order status changes to processing.
	 *
	 * @param int $order_id Order ID.
	 */
	public function process_banner_order( $order_id ) {
		$order = wc_get_order( $order_id );
		if ( ! $order ) {
			return;
		}

		// Check if this order contains banner products
		$banner_items = $this->get_banner_items_from_order( $order );
		if ( empty( $banner_items ) ) {
			return;
		}

		// Mark order as containing banner products
		$order->update_meta_data( '_contains_banner_products', 'yes' );
		$order->save();

		// Send notification to customer about next steps
		$this->send_banner_order_notification( $order );
	}

	/**
	 * Activate banner campaign when order is completed.
	 *
	 * @param int $order_id Order ID.
	 */
	public function activate_banner_campaign( $order_id ) {
		$order = wc_get_order( $order_id );
		if ( ! $order ) {
			return;
		}

		// Check if this order contains banner products
		$banner_items = $this->get_banner_items_from_order( $order );
		if ( empty( $banner_items ) ) {
			return;
		}

		// Check if campaigns have already been activated for this order
		if ( $order->get_meta( '_banner_campaigns_activated' ) ) {
			return;
		}

		$customer_id = $order->get_customer_id();
		
		foreach ( $banner_items as $item ) {
			$product = $item->get_product();
			$duration_days = $product->get_meta( '_banner_duration_days' );
			
			// Look for pending campaigns from this customer
			$pending_campaigns = $this->get_pending_campaigns_for_customer( $customer_id );
			
			if ( ! empty( $pending_campaigns ) ) {
				// Activate the first pending campaign
				$campaign = array_shift( $pending_campaigns );
				$this->activate_campaign( $campaign, $order_id, $duration_days );
			}
		}

		// Mark campaigns as activated for this order
		$order->update_meta_data( '_banner_campaigns_activated', 'yes' );
		$order->save();
	}

	/**
	 * Get banner items from an order.
	 *
	 * @param WC_Order $order Order object.
	 * @return array Array of banner items.
	 */
	private function get_banner_items_from_order( $order ) {
		$banner_items = array();
		
		foreach ( $order->get_items() as $item ) {
			$product = $item->get_product();
			if ( $product && $product->get_meta( '_is_banner_product' ) === 'yes' ) {
				$banner_items[] = $item;
			}
		}
		
		return $banner_items;
	}

	/**
	 * Get pending campaigns for a customer.
	 *
	 * @param int $customer_id Customer ID.
	 * @return array Array of campaign post objects.
	 */
	private function get_pending_campaigns_for_customer( $customer_id ) {
		$args = array(
			'post_type' => 'banner_campaign',
			'post_status' => 'publish',
			'author' => $customer_id,
			'meta_query' => array(
				array(
					'key' => '_campaign_status',
					'value' => 'pending',
					'compare' => '='
				)
			),
			'orderby' => 'date',
			'order' => 'ASC'
		);

		return get_posts( $args );
	}

	/**
	 * Activate a campaign.
	 *
	 * @param WP_Post $campaign     Campaign post object.
	 * @param int     $order_id     Order ID.
	 * @param int     $duration_days Duration in days.
	 */
	private function activate_campaign( $campaign, $order_id, $duration_days ) {
		$start_date = current_time( 'Y-m-d H:i:s' );
		$end_date = date( 'Y-m-d H:i:s', strtotime( $start_date . ' + ' . $duration_days . ' days' ) );

		// Update campaign meta
		update_post_meta( $campaign->ID, '_campaign_status', 'active' );
		update_post_meta( $campaign->ID, '_campaign_start_date', $start_date );
		update_post_meta( $campaign->ID, '_campaign_end_date', $end_date );
		update_post_meta( $campaign->ID, '_woocommerce_order_id', $order_id );
		update_post_meta( $campaign->ID, '_campaign_duration_days', $duration_days );

		// Update taxonomy term
		wp_set_post_terms( $campaign->ID, array( 'active' ), 'campaign_status' );

		// Schedule campaign expiration
		wp_schedule_single_event( strtotime( $end_date ), 'banner_ads_expire_campaign', array( $campaign->ID ) );

		// Send activation notification
		$this->send_campaign_activation_notification( $campaign, $order_id );
	}

	/**
	 * Send banner order notification to customer.
	 *
	 * @param WC_Order $order Order object.
	 */
	private function send_banner_order_notification( $order ) {
		$customer_email = $order->get_billing_email();
		$customer_name = $order->get_billing_first_name();

		$subject = __( 'Banner Ad Order Received - Next Steps', 'real-estate-banner-ads' );
		
		$message = sprintf(
			__( 'Hi %s,

Thank you for your banner ad order (#%s). 

To activate your banner ad campaign, please:

1. Upload your banner ad using our upload form
2. Choose your preferred ad zone
3. Wait for approval (if required)

Once approved and your payment is processed, your banner ad will go live automatically.

You can manage your campaigns and view analytics in your dashboard.

Best regards,
The Team', 'real-estate-banner-ads' ),
			$customer_name,
			$order->get_order_number()
		);

		wp_mail( $customer_email, $subject, $message );
	}

	/**
	 * Send campaign activation notification.
	 *
	 * @param WP_Post $campaign Campaign post object.
	 * @param int     $order_id Order ID.
	 */
	private function send_campaign_activation_notification( $campaign, $order_id ) {
		$customer_id = $campaign->post_author;
		$user = get_user_by( 'id', $customer_id );
		
		if ( ! $user ) {
			return;
		}

		$order = wc_get_order( $order_id );
		$start_date = get_post_meta( $campaign->ID, '_campaign_start_date', true );
		$end_date = get_post_meta( $campaign->ID, '_campaign_end_date', true );

		$subject = __( 'Your Banner Ad Campaign is Now Live!', 'real-estate-banner-ads' );
		
		$message = sprintf(
			__( 'Hi %s,

Great news! Your banner ad campaign "%s" is now live.

Campaign Details:
- Start Date: %s
- End Date: %s
- Order: #%s

You can track your campaign performance in your dashboard.

Best regards,
The Team', 'real-estate-banner-ads' ),
			$user->display_name,
			$campaign->post_title,
			date( 'F j, Y', strtotime( $start_date ) ),
			date( 'F j, Y', strtotime( $end_date ) ),
			$order->get_order_number()
		);

		wp_mail( $user->user_email, $subject, $message );
	}
}
