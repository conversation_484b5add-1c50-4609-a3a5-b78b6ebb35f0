# Media Uploader Fix Guide

## 🔧 **Issue Fixed: Maximum Call Stack Size Exceeded**

The JavaScript error "Maximum call stack size exceeded" was caused by infinite recursion in the media uploader event handlers. This has been resolved with the following improvements:

## ✅ **What Was Fixed:**

### **1. Event Handler Conflicts**
- **Removed duplicate event bindings** that caused infinite loops
- **Added event delegation** to prevent multiple handlers
- **Implemented initialization guards** to prevent re-initialization

### **2. Media Library Integration**
- **Properly enqueued wp.media** for campaign edit pages
- **Added error handling** for missing media library
- **Improved script loading order** (moved to footer)

### **3. JavaScript Improvements**
- **Added try-catch blocks** for error handling
- **Prevented multiple clicks** during upload process
- **Added loading states** for better UX

## 🚀 **How to Test the Fix:**

### **1. Test Campaign Creation:**
1. Go to **Banner Ads > Banner Campaigns > Add New**
2. Select **"Image Banner"** type
3. Click **"Upload Image"** button
4. Media library should open without errors
5. Select an image and click **"Choose Image"**
6. Image should appear in preview area

### **2. Test Campaign Editing:**
1. Go to **Banner Ads > All Campaigns**
2. Edit an existing campaign
3. Try uploading a new image
4. Should work without JavaScript errors

### **3. Check Browser Console:**
1. Open browser developer tools (F12)
2. Go to Console tab
3. Try uploading an image
4. Should see no "Maximum call stack" errors

## 🔍 **Debug Information:**

If you still encounter issues, check the browser console for these messages:

### **Success Messages:**
- No JavaScript errors
- Image preview updates correctly
- Button states change properly

### **Error Messages to Look For:**
- `"WordPress media library is not available"`
- `"Error opening media uploader"`
- `"Error selecting image"`

## 🛠 **Additional Troubleshooting:**

### **If Upload Button Still Doesn't Work:**

1. **Clear Browser Cache:**
   - Hard refresh: Ctrl+F5 (Windows) or Cmd+Shift+R (Mac)
   - Clear browser cache completely

2. **Check Plugin Conflicts:**
   - Temporarily deactivate other plugins
   - Test if media uploader works
   - Reactivate plugins one by one

3. **Verify WordPress Media Library:**
   - Go to **Media > Library** in WordPress admin
   - Try uploading an image there
   - If that doesn't work, it's a WordPress issue

4. **Check File Permissions:**
   - Ensure `/wp-content/uploads/` is writable
   - Check server error logs

### **Manual Debug Steps:**

1. **Open Browser Console** (F12)
2. **Go to campaign edit page**
3. **Type this in console:**
   ```javascript
   console.log('wp.media available:', typeof wp !== 'undefined' && typeof wp.media !== 'undefined');
   console.log('jQuery available:', typeof jQuery !== 'undefined');
   ```
4. **Both should return `true`**

## 📝 **Technical Details:**

### **Changes Made:**

1. **admin/class-banner-ads-admin.php:**
   - Added `wp_enqueue_media()` for campaign pages
   - Improved script dependencies
   - Moved scripts to footer for better loading

2. **admin/js/banner-ads-admin.js:**
   - Added initialization guards
   - Implemented proper event delegation
   - Added comprehensive error handling
   - Prevented multiple event bindings

3. **Removed Inline JavaScript:**
   - Moved media uploader logic to external JS file
   - Simplified inline scripts to prevent conflicts

### **Key Improvements:**

- **No more infinite recursion**
- **Proper error handling**
- **Better user feedback**
- **Conflict prevention**
- **Debugging capabilities**

## ✅ **Expected Behavior:**

After the fix:

1. **Click "Upload Image"** → Media library opens
2. **Select image** → Preview appears immediately
3. **Button shows loading state** during process
4. **No JavaScript errors** in console
5. **Smooth user experience** throughout

## 🎯 **If Issues Persist:**

1. **Check WordPress version** (should be 6.0+)
2. **Verify theme compatibility**
3. **Test with default WordPress theme**
4. **Check server PHP version** (should be 7.4+)

The media uploader should now work smoothly without any JavaScript errors! 🎉
