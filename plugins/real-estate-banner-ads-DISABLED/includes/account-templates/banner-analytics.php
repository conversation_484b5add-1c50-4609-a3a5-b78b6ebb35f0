<?php
/**
 * Account page template for banner analytics
 *
 * @package RealEstateBannerAds
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// Get user's campaigns for filter
$user_campaigns_posts = get_posts( array(
	'post_type' => 'banner_campaign',
	'author' => $user_id,
	'posts_per_page' => -1,
	'post_status' => 'publish',
	'orderby' => 'title',
	'order' => 'ASC'
));
?>

<div class="woocommerce-banner-analytics">
	
	<!-- Analytics Filters -->
	<div class="analytics-filters">
		<form method="get" class="analytics-filter-form">
			<div class="filter-row">
				<div class="filter-group">
					<label for="date_range"><?php esc_html_e( 'Date Range:', 'real-estate-banner-ads' ); ?></label>
					<select name="date_range" id="date_range" class="select">
						<option value="7days" <?php selected( $date_range, '7days' ); ?>><?php esc_html_e( 'Last 7 Days', 'real-estate-banner-ads' ); ?></option>
						<option value="30days" <?php selected( $date_range, '30days' ); ?>><?php esc_html_e( 'Last 30 Days', 'real-estate-banner-ads' ); ?></option>
						<option value="90days" <?php selected( $date_range, '90days' ); ?>><?php esc_html_e( 'Last 90 Days', 'real-estate-banner-ads' ); ?></option>
					</select>
				</div>

				<div class="filter-group">
					<label for="campaign_filter"><?php esc_html_e( 'Campaign:', 'real-estate-banner-ads' ); ?></label>
					<select name="campaign_filter" id="campaign_filter" class="select">
						<option value=""><?php esc_html_e( 'All Campaigns', 'real-estate-banner-ads' ); ?></option>
						<?php foreach ( $user_campaigns_posts as $campaign ) : ?>
							<option value="<?php echo esc_attr( $campaign->ID ); ?>" <?php selected( $campaign_filter, $campaign->ID ); ?>>
								<?php echo esc_html( $campaign->post_title ); ?>
							</option>
						<?php endforeach; ?>
					</select>
				</div>

				<div class="filter-group">
					<button type="submit" class="button"><?php esc_html_e( 'Apply Filters', 'real-estate-banner-ads' ); ?></button>
				</div>
			</div>
		</form>
	</div>

	<!-- Summary Stats -->
	<div class="analytics-summary">
		<div class="summary-stats">
			<div class="stat-box">
				<div class="stat-number"><?php echo esc_html( number_format( $analytics_summary['total_impressions'] ) ); ?></div>
				<div class="stat-label"><?php esc_html_e( 'Total Impressions', 'real-estate-banner-ads' ); ?></div>
			</div>
			<div class="stat-box">
				<div class="stat-number"><?php echo esc_html( number_format( $analytics_summary['total_clicks'] ) ); ?></div>
				<div class="stat-label"><?php esc_html_e( 'Total Clicks', 'real-estate-banner-ads' ); ?></div>
			</div>
			<div class="stat-box">
				<div class="stat-number"><?php echo esc_html( $analytics_summary['average_ctr'] ); ?>%</div>
				<div class="stat-label"><?php esc_html_e( 'Average CTR', 'real-estate-banner-ads' ); ?></div>
			</div>
			<div class="stat-box">
				<div class="stat-number"><?php echo esc_html( count( $analytics_summary['campaigns'] ) ); ?></div>
				<div class="stat-label"><?php esc_html_e( 'Active Campaigns', 'real-estate-banner-ads' ); ?></div>
			</div>
		</div>
	</div>

	<!-- Campaign Performance Table -->
	<?php if ( ! empty( $analytics_summary['campaigns'] ) ) : ?>
		<div class="campaign-performance">
			<h3><?php esc_html_e( 'Campaign Performance', 'real-estate-banner-ads' ); ?></h3>
			
			<div class="performance-table-wrapper">
				<table class="shop_table shop_table_responsive campaign_performance_table">
					<thead>
						<tr>
							<th class="campaign-name"><?php esc_html_e( 'Campaign', 'real-estate-banner-ads' ); ?></th>
							<th class="campaign-zone"><?php esc_html_e( 'Zone', 'real-estate-banner-ads' ); ?></th>
							<th class="campaign-status"><?php esc_html_e( 'Status', 'real-estate-banner-ads' ); ?></th>
							<th class="campaign-impressions"><?php esc_html_e( 'Impressions', 'real-estate-banner-ads' ); ?></th>
							<th class="campaign-clicks"><?php esc_html_e( 'Clicks', 'real-estate-banner-ads' ); ?></th>
							<th class="campaign-ctr"><?php esc_html_e( 'CTR', 'real-estate-banner-ads' ); ?></th>
						</tr>
					</thead>
					<tbody>
						<?php foreach ( $analytics_summary['campaigns'] as $campaign_data ) : ?>
							<?php
							$campaign_post = get_post( $campaign_data['campaign_id'] );
							if ( ! $campaign_post || $campaign_post->post_author != $user_id ) {
								continue; // Skip campaigns not owned by current user
							}
							
							$campaign_status = get_post_meta( $campaign_post->ID, '_campaign_status', true );
							$campaign_zone = get_post_meta( $campaign_post->ID, '_banner_zone', true );
							
							$zones = get_option( 'banner_ads_zones', array() );
							$zone_name = $zones[ $campaign_zone ]['name'] ?? $campaign_zone;
							?>
							<tr>
								<td class="campaign-name" data-title="<?php esc_attr_e( 'Campaign', 'real-estate-banner-ads' ); ?>">
									<strong><?php echo esc_html( $campaign_post->post_title ); ?></strong>
								</td>
								<td class="campaign-zone" data-title="<?php esc_attr_e( 'Zone', 'real-estate-banner-ads' ); ?>">
									<?php echo esc_html( $zone_name ); ?>
								</td>
								<td class="campaign-status" data-title="<?php esc_attr_e( 'Status', 'real-estate-banner-ads' ); ?>">
									<span class="status-badge status-<?php echo esc_attr( $campaign_status ); ?>">
										<?php echo esc_html( ucfirst( $campaign_status ) ); ?>
									</span>
								</td>
								<td class="campaign-impressions" data-title="<?php esc_attr_e( 'Impressions', 'real-estate-banner-ads' ); ?>">
									<?php echo esc_html( number_format( $campaign_data['impressions'] ) ); ?>
								</td>
								<td class="campaign-clicks" data-title="<?php esc_attr_e( 'Clicks', 'real-estate-banner-ads' ); ?>">
									<?php echo esc_html( number_format( $campaign_data['clicks'] ) ); ?>
								</td>
								<td class="campaign-ctr" data-title="<?php esc_attr_e( 'CTR', 'real-estate-banner-ads' ); ?>">
									<?php echo esc_html( $campaign_data['ctr'] ); ?>%
								</td>
							</tr>
						<?php endforeach; ?>
					</tbody>
				</table>
			</div>
		</div>
	<?php else : ?>
		<div class="woocommerce-message woocommerce-message--info">
			<p><?php esc_html_e( 'No campaign data found for the selected period.', 'real-estate-banner-ads' ); ?></p>
			<p>
				<a href="<?php echo esc_url( wc_get_account_endpoint_url( 'upload-banner' ) ); ?>" class="button button-primary">
					<?php esc_html_e( 'Create Your First Campaign', 'real-estate-banner-ads' ); ?>
				</a>
			</p>
		</div>
	<?php endif; ?>

	<!-- Performance Tips -->
	<div class="performance-tips">
		<h3><?php esc_html_e( 'Performance Tips', 'real-estate-banner-ads' ); ?></h3>
		<div class="tips-grid">
			<div class="tip-card">
				<h4><?php esc_html_e( 'Optimize Your Images', 'real-estate-banner-ads' ); ?></h4>
				<p><?php esc_html_e( 'Use high-quality, eye-catching images that are properly sized for your chosen ad zone.', 'real-estate-banner-ads' ); ?></p>
			</div>
			<div class="tip-card">
				<h4><?php esc_html_e( 'Clear Call-to-Action', 'real-estate-banner-ads' ); ?></h4>
				<p><?php esc_html_e( 'Include a clear and compelling call-to-action to encourage clicks.', 'real-estate-banner-ads' ); ?></p>
			</div>
			<div class="tip-card">
				<h4><?php esc_html_e( 'Target the Right Zone', 'real-estate-banner-ads' ); ?></h4>
				<p><?php esc_html_e( 'Choose ad zones that align with your target audience and campaign goals.', 'real-estate-banner-ads' ); ?></p>
			</div>
			<div class="tip-card">
				<h4><?php esc_html_e( 'Monitor Performance', 'real-estate-banner-ads' ); ?></h4>
				<p><?php esc_html_e( 'Regularly check your analytics to understand what works best for your campaigns.', 'real-estate-banner-ads' ); ?></p>
			</div>
		</div>
	</div>

	<!-- Quick Actions -->
	<div class="analytics-actions">
		<a href="<?php echo esc_url( wc_get_account_endpoint_url( 'banner-campaigns' ) ); ?>" class="button">
			<?php esc_html_e( 'Back to Campaigns', 'real-estate-banner-ads' ); ?>
		</a>
		<a href="<?php echo esc_url( wc_get_account_endpoint_url( 'upload-banner' ) ); ?>" class="button button-primary">
			<?php esc_html_e( 'Create New Campaign', 'real-estate-banner-ads' ); ?>
		</a>
	</div>
</div>

<script>
jQuery(document).ready(function($) {
	// Auto-submit form on filter change
	$('#date_range, #campaign_filter').on('change', function() {
		$(this).closest('form').submit();
	});
});
</script>
