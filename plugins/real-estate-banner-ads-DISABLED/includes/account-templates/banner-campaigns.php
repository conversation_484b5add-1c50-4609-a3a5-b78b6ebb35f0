<?php
/**
 * Account page template for banner campaigns
 *
 * @package RealEstateBannerAds
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}
?>

<div class="woocommerce-banner-campaigns">
	
	<?php if ( current_user_can( 'manage_options' ) ) : ?>
		<div class="woocommerce-message woocommerce-message--info">
			<strong><?php esc_html_e( 'Admin Mode:', 'real-estate-banner-ads' ); ?></strong> 
			<?php esc_html_e( 'You have full access to create and manage banner campaigns without restrictions.', 'real-estate-banner-ads' ); ?>
			<a href="<?php echo esc_url( admin_url( 'admin.php?page=banner-ads' ) ); ?>" class="button button-small" style="margin-left: 10px;">
				<?php esc_html_e( 'Admin Dashboard', 'real-estate-banner-ads' ); ?>
			</a>
		</div>
	<?php endif; ?>

	<!-- Summary Stats -->
	<div class="banner-campaigns-summary">
		<div class="summary-stats">
			<div class="stat-box">
				<div class="stat-number"><?php echo esc_html( $total_campaigns ); ?></div>
				<div class="stat-label"><?php esc_html_e( 'Total Campaigns', 'real-estate-banner-ads' ); ?></div>
			</div>
			<div class="stat-box">
				<div class="stat-number"><?php echo esc_html( $active_campaigns ); ?></div>
				<div class="stat-label"><?php esc_html_e( 'Active Campaigns', 'real-estate-banner-ads' ); ?></div>
			</div>
			<div class="stat-box">
				<div class="stat-number"><?php echo esc_html( number_format( $total_impressions ) ); ?></div>
				<div class="stat-label"><?php esc_html_e( 'Total Views', 'real-estate-banner-ads' ); ?></div>
			</div>
			<div class="stat-box">
				<div class="stat-number"><?php echo esc_html( number_format( $total_clicks ) ); ?></div>
				<div class="stat-label"><?php esc_html_e( 'Total Clicks', 'real-estate-banner-ads' ); ?></div>
			</div>
			<div class="stat-box">
				<div class="stat-number"><?php echo esc_html( $average_ctr ); ?>%</div>
				<div class="stat-label"><?php esc_html_e( 'Average CTR', 'real-estate-banner-ads' ); ?></div>
			</div>
		</div>
	</div>

	<!-- Quick Actions -->
	<div class="banner-campaigns-actions">
		<a href="<?php echo esc_url( wc_get_account_endpoint_url( 'upload-banner' ) ); ?>" class="button button-primary">
			<?php esc_html_e( 'Upload New Banner', 'real-estate-banner-ads' ); ?>
		</a>
		
		<?php if ( ! current_user_can( 'manage_options' ) ) : ?>
			<a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="button">
				<?php esc_html_e( 'Buy Ad Package', 'real-estate-banner-ads' ); ?>
			</a>
		<?php else : ?>
			<a href="<?php echo esc_url( admin_url( 'post-new.php?post_type=banner_campaign' ) ); ?>" class="button">
				<?php esc_html_e( 'Create Campaign (Admin)', 'real-estate-banner-ads' ); ?>
			</a>
		<?php endif; ?>
		
		<a href="<?php echo esc_url( wc_get_account_endpoint_url( 'banner-analytics' ) ); ?>" class="button">
			<?php esc_html_e( 'View Analytics', 'real-estate-banner-ads' ); ?>
		</a>
	</div>

	<!-- Campaigns List -->
	<div class="banner-campaigns-list">
		<?php if ( ! empty( $user_campaigns ) ) : ?>
			<h3><?php esc_html_e( 'Your Banner Campaigns', 'real-estate-banner-ads' ); ?></h3>
			
			<div class="campaigns-table-wrapper">
				<table class="shop_table shop_table_responsive banner_campaigns_table">
					<thead>
						<tr>
							<th class="campaign-preview"><?php esc_html_e( 'Preview', 'real-estate-banner-ads' ); ?></th>
							<th class="campaign-title"><?php esc_html_e( 'Campaign', 'real-estate-banner-ads' ); ?></th>
							<th class="campaign-zone"><?php esc_html_e( 'Zone', 'real-estate-banner-ads' ); ?></th>
							<th class="campaign-status"><?php esc_html_e( 'Status', 'real-estate-banner-ads' ); ?></th>
							<th class="campaign-dates"><?php esc_html_e( 'Duration', 'real-estate-banner-ads' ); ?></th>
							<th class="campaign-performance"><?php esc_html_e( 'Performance', 'real-estate-banner-ads' ); ?></th>
						</tr>
					</thead>
					<tbody>
						<?php foreach ( $user_campaigns as $campaign_post ) : ?>
							<?php
							$campaign_id = $campaign_post->ID;
							$status = get_post_meta( $campaign_id, '_campaign_status', true );
							$zone = get_post_meta( $campaign_id, '_banner_zone', true );
							$start_date = get_post_meta( $campaign_id, '_campaign_start_date', true );
							$end_date = get_post_meta( $campaign_id, '_campaign_end_date', true );
							$banner_type = get_post_meta( $campaign_id, '_banner_type', true );
							$image_url = get_post_meta( $campaign_id, '_banner_image_url', true );
							
							$impressions = Banner_Analytics::get_impressions( $campaign_id );
							$clicks = Banner_Analytics::get_clicks( $campaign_id );
							$ctr = $impressions > 0 ? round( ( $clicks / $impressions ) * 100, 2 ) : 0;
							
							$zones = get_option( 'banner_ads_zones', array() );
							$zone_name = $zones[ $zone ]['name'] ?? $zone;
							?>
							<tr class="campaign-row">
								<td class="campaign-preview" data-title="<?php esc_attr_e( 'Preview', 'real-estate-banner-ads' ); ?>">
									<?php if ( $banner_type === 'image' && $image_url ) : ?>
										<img src="<?php echo esc_url( $image_url ); ?>" alt="<?php echo esc_attr( $campaign_post->post_title ); ?>" class="campaign-thumbnail">
									<?php else : ?>
										<div class="campaign-thumbnail html-banner">
											<span><?php esc_html_e( 'HTML', 'real-estate-banner-ads' ); ?></span>
										</div>
									<?php endif; ?>
								</td>
								<td class="campaign-title" data-title="<?php esc_attr_e( 'Campaign', 'real-estate-banner-ads' ); ?>">
									<strong><?php echo esc_html( $campaign_post->post_title ); ?></strong>
									<div class="campaign-meta">
										<small><?php echo esc_html( ucfirst( $banner_type ) ); ?> Banner</small>
									</div>
								</td>
								<td class="campaign-zone" data-title="<?php esc_attr_e( 'Zone', 'real-estate-banner-ads' ); ?>">
									<?php echo esc_html( $zone_name ); ?>
								</td>
								<td class="campaign-status" data-title="<?php esc_attr_e( 'Status', 'real-estate-banner-ads' ); ?>">
									<span class="status-badge status-<?php echo esc_attr( $status ); ?>">
										<?php echo esc_html( ucfirst( $status ) ); ?>
									</span>
								</td>
								<td class="campaign-dates" data-title="<?php esc_attr_e( 'Duration', 'real-estate-banner-ads' ); ?>">
									<?php if ( $start_date && $end_date ) : ?>
										<div class="date-range">
											<div><strong><?php esc_html_e( 'Start:', 'real-estate-banner-ads' ); ?></strong> <?php echo esc_html( date( 'M j, Y', strtotime( $start_date ) ) ); ?></div>
											<div><strong><?php esc_html_e( 'End:', 'real-estate-banner-ads' ); ?></strong> <?php echo esc_html( date( 'M j, Y', strtotime( $end_date ) ) ); ?></div>
										</div>
									<?php else : ?>
										<span class="no-dates"><?php esc_html_e( 'Not scheduled', 'real-estate-banner-ads' ); ?></span>
									<?php endif; ?>
								</td>
								<td class="campaign-performance" data-title="<?php esc_attr_e( 'Performance', 'real-estate-banner-ads' ); ?>">
									<div class="performance-stats">
										<div class="stat-item">
											<span class="stat-value"><?php echo esc_html( number_format( $impressions ) ); ?></span>
											<span class="stat-label"><?php esc_html_e( 'Views', 'real-estate-banner-ads' ); ?></span>
										</div>
										<div class="stat-item">
											<span class="stat-value"><?php echo esc_html( number_format( $clicks ) ); ?></span>
											<span class="stat-label"><?php esc_html_e( 'Clicks', 'real-estate-banner-ads' ); ?></span>
										</div>
										<div class="stat-item">
											<span class="stat-value"><?php echo esc_html( $ctr ); ?>%</span>
											<span class="stat-label"><?php esc_html_e( 'CTR', 'real-estate-banner-ads' ); ?></span>
										</div>
									</div>
								</td>
							</tr>
						<?php endforeach; ?>
					</tbody>
				</table>
			</div>
		<?php else : ?>
			<div class="woocommerce-message woocommerce-message--info">
				<p><?php esc_html_e( 'You haven\'t created any banner campaigns yet.', 'real-estate-banner-ads' ); ?></p>
				<p>
					<a href="<?php echo esc_url( wc_get_account_endpoint_url( 'upload-banner' ) ); ?>" class="button button-primary">
						<?php esc_html_e( 'Create Your First Campaign', 'real-estate-banner-ads' ); ?>
					</a>
					<?php if ( ! current_user_can( 'manage_options' ) ) : ?>
						<a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="button">
							<?php esc_html_e( 'Browse Ad Packages', 'real-estate-banner-ads' ); ?>
						</a>
					<?php endif; ?>
				</p>
			</div>
		<?php endif; ?>
	</div>

	<!-- Recent Orders (for non-admins) -->
	<?php if ( ! current_user_can( 'manage_options' ) && class_exists( 'WooCommerce' ) ) : ?>
		<?php
		$customer_orders = wc_get_orders( array(
			'customer' => $user_id,
			'limit' => 3,
			'orderby' => 'date',
			'order' => 'DESC',
			'meta_query' => array(
				array(
					'key' => '_contains_banner_products',
					'value' => 'yes',
					'compare' => '='
				)
			)
		));
		?>
		
		<?php if ( ! empty( $customer_orders ) ) : ?>
			<div class="recent-banner-orders">
				<h3><?php esc_html_e( 'Recent Banner Ad Orders', 'real-estate-banner-ads' ); ?></h3>
				<table class="shop_table shop_table_responsive">
					<thead>
						<tr>
							<th><?php esc_html_e( 'Order', 'real-estate-banner-ads' ); ?></th>
							<th><?php esc_html_e( 'Date', 'real-estate-banner-ads' ); ?></th>
							<th><?php esc_html_e( 'Status', 'real-estate-banner-ads' ); ?></th>
							<th><?php esc_html_e( 'Total', 'real-estate-banner-ads' ); ?></th>
						</tr>
					</thead>
					<tbody>
						<?php foreach ( $customer_orders as $order ) : ?>
							<tr>
								<td data-title="<?php esc_attr_e( 'Order', 'real-estate-banner-ads' ); ?>">
									<a href="<?php echo esc_url( $order->get_view_order_url() ); ?>">
										#<?php echo esc_html( $order->get_order_number() ); ?>
									</a>
								</td>
								<td data-title="<?php esc_attr_e( 'Date', 'real-estate-banner-ads' ); ?>">
									<?php echo esc_html( $order->get_date_created()->date( 'M j, Y' ) ); ?>
								</td>
								<td data-title="<?php esc_attr_e( 'Status', 'real-estate-banner-ads' ); ?>">
									<span class="status-badge status-<?php echo esc_attr( $order->get_status() ); ?>">
										<?php echo esc_html( wc_get_order_status_name( $order->get_status() ) ); ?>
									</span>
								</td>
								<td data-title="<?php esc_attr_e( 'Total', 'real-estate-banner-ads' ); ?>">
									<?php echo wp_kses_post( $order->get_formatted_order_total() ); ?>
								</td>
							</tr>
						<?php endforeach; ?>
					</tbody>
				</table>
			</div>
		<?php endif; ?>
	<?php endif; ?>
</div>
