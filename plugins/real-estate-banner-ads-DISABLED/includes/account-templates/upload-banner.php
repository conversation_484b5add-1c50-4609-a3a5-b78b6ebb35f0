<?php
/**
 * Account page template for banner upload
 *
 * @package RealEstateBannerAds
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$zones = Banner_Zone::get_options_with_availability( true );
?>

<div class="woocommerce-upload-banner">
	
	<?php if ( current_user_can( 'manage_options' ) ) : ?>
		<div class="woocommerce-message woocommerce-message--info">
			<strong><?php esc_html_e( 'Admin Mode:', 'real-estate-banner-ads' ); ?></strong> 
			<?php esc_html_e( 'Your campaigns will be automatically activated without requiring payment.', 'real-estate-banner-ads' ); ?>
		</div>
	<?php endif; ?>

	<div id="upload-response" class="upload-response" style="display: none;"></div>

	<form id="banner-upload-form" class="banner-upload-form" method="post" enctype="multipart/form-data">
		<?php wp_nonce_field( 'banner_upload_nonce', 'banner_upload_nonce' ); ?>
		
		<!-- Campaign Title -->
		<p class="form-row form-row-wide">
			<label for="campaign_title"><?php esc_html_e( 'Campaign Title', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label>
			<input type="text" class="input-text" name="campaign_title" id="campaign_title" required>
		</p>

		<!-- Banner Type Selection -->
		<p class="form-row form-row-wide">
			<label><?php esc_html_e( 'Banner Type', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label>
			<div class="banner-type-selection">
				<label class="banner-type-option active">
					<input type="radio" name="banner_type" value="image" checked>
					<div class="option-content">
						<strong><?php esc_html_e( 'Image Banner', 'real-estate-banner-ads' ); ?></strong>
						<small><?php esc_html_e( 'Upload JPG, PNG, or GIF image', 'real-estate-banner-ads' ); ?></small>
					</div>
				</label>
				<label class="banner-type-option">
					<input type="radio" name="banner_type" value="html">
					<div class="option-content">
						<strong><?php esc_html_e( 'HTML Banner', 'real-estate-banner-ads' ); ?></strong>
						<small><?php esc_html_e( 'Custom HTML/CSS code', 'real-estate-banner-ads' ); ?></small>
					</div>
				</label>
			</div>
		</p>

		<!-- Image Upload Fields -->
		<div class="conditional-fields image-fields active">
			<p class="form-row form-row-wide">
				<label for="banner_image"><?php esc_html_e( 'Banner Image', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label>
				<div class="file-upload-area" id="file-upload-area">
					<div class="upload-content">
						<div class="upload-icon">📁</div>
						<div class="upload-text">
							<strong><?php esc_html_e( 'Click to upload', 'real-estate-banner-ads' ); ?></strong>
							<?php esc_html_e( 'or drag and drop', 'real-estate-banner-ads' ); ?>
						</div>
						<div class="upload-requirements">
							<small><?php esc_html_e( 'Maximum file size: 2MB. Supported formats: JPG, PNG, GIF', 'real-estate-banner-ads' ); ?></small>
						</div>
					</div>
					<input type="file" id="banner_image" name="banner_image" accept="image/*" style="display: none;">
					<!-- Fallback button for browsers that block hidden file input clicks -->
					<button type="button" id="fallback-upload-btn" class="button button-secondary" style="display: none; margin-top: 15px;">
						<?php esc_html_e( 'Choose File', 'real-estate-banner-ads' ); ?>
					</button>
				</div>
				<div class="file-info" id="file-info" style="display: none;">
					<div class="file-preview"></div>
					<div class="file-details">
						<div class="file-name"></div>
						<div class="file-size"></div>
					</div>
				</div>
			</p>

			<p class="form-row form-row-wide">
				<label for="alt_text"><?php esc_html_e( 'Alt Text', 'real-estate-banner-ads' ); ?></label>
				<input type="text" class="input-text" name="alt_text" id="alt_text" placeholder="<?php esc_attr_e( 'Describe your image for accessibility', 'real-estate-banner-ads' ); ?>">
			</p>
		</div>

		<!-- HTML Content Fields -->
		<div class="conditional-fields html-fields">
			<p class="form-row form-row-wide">
				<label for="html_content"><?php esc_html_e( 'HTML Content', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label>
				<textarea name="html_content" id="html_content" class="input-text" rows="8" placeholder="<?php esc_attr_e( 'Enter your HTML/CSS code here...', 'real-estate-banner-ads' ); ?>"></textarea>
				<small><?php esc_html_e( 'You can include HTML and CSS. JavaScript is not allowed for security reasons.', 'real-estate-banner-ads' ); ?></small>
			</p>
		</div>

		<!-- Link URL -->
		<p class="form-row form-row-wide">
			<label for="link_url"><?php esc_html_e( 'Link URL', 'real-estate-banner-ads' ); ?></label>
			<input type="url" class="input-text" name="link_url" id="link_url" placeholder="https://example.com">
		</p>

		<!-- Link Target and Zone -->
		<p class="form-row form-row-first">
			<label for="link_target"><?php esc_html_e( 'Link Target', 'real-estate-banner-ads' ); ?></label>
			<select name="link_target" id="link_target" class="select">
				<option value="_blank"><?php esc_html_e( 'New Window', 'real-estate-banner-ads' ); ?></option>
				<option value="_self"><?php esc_html_e( 'Same Window', 'real-estate-banner-ads' ); ?></option>
			</select>
		</p>

		<p class="form-row form-row-last">
			<label for="banner_zone"><?php esc_html_e( 'Ad Zone', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label>
			<select name="banner_zone" id="banner_zone" class="select" required>
				<option value=""><?php esc_html_e( 'Select a zone...', 'real-estate-banner-ads' ); ?></option>
				<?php foreach ( $zones as $zone_id => $zone_data ) : ?>
					<option value="<?php echo esc_attr( $zone_id ); ?>"
							<?php echo $zone_data['is_full'] ? 'disabled' : ''; ?>
							data-available-slots="<?php echo esc_attr( $zone_data['available_slots'] ); ?>"
							data-max-banners="<?php echo esc_attr( $zone_data['max_banners'] ); ?>"
							data-dimensions="<?php echo esc_attr( $zone_data['dimensions'] ); ?>">
						<?php echo esc_html( $zone_data['name'] ); ?>
					</option>
				<?php endforeach; ?>
			</select>
			<small class="zone-help-text" style="display: none; margin-top: 5px; color: #666;"></small>
		</p>

		<div class="clear"></div>

		<!-- Submit Button -->
		<p class="form-row">
			<button type="submit" class="button button-primary" id="submit-banner">
				<?php esc_html_e( 'Submit Banner Campaign', 'real-estate-banner-ads' ); ?>
			</button>
			<span class="loading-spinner" style="display: none;">
				<?php esc_html_e( 'Uploading...', 'real-estate-banner-ads' ); ?>
			</span>
		</p>
	</form>

	<!-- Next Steps -->
	<?php if ( ! current_user_can( 'manage_options' ) ) : ?>
		<div class="next-steps-info">
			<h3><?php esc_html_e( 'Next Steps', 'real-estate-banner-ads' ); ?></h3>
			<ol>
				<li><?php esc_html_e( 'Submit your banner campaign using the form above', 'real-estate-banner-ads' ); ?></li>
				<li><?php esc_html_e( 'Purchase an ad package from our shop', 'real-estate-banner-ads' ); ?></li>
				<li><?php esc_html_e( 'Your campaign will be reviewed and activated automatically', 'real-estate-banner-ads' ); ?></li>
				<li><?php esc_html_e( 'Track your campaign performance in your dashboard', 'real-estate-banner-ads' ); ?></li>
			</ol>
			<p>
				<a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="button">
					<?php esc_html_e( 'Browse Ad Packages', 'real-estate-banner-ads' ); ?>
				</a>
			</p>
		</div>
	<?php endif; ?>

	<!-- Zone Information -->
	<div class="zone-information">
		<h3><?php esc_html_e( 'Available Ad Zones', 'real-estate-banner-ads' ); ?></h3>
		<div class="zones-grid">
			<?php
			$all_zones = Banner_Zone::get_all( true );
			foreach ( $all_zones as $zone ) :
				$available_slots = $zone->get_available_slots();
			?>
				<div class="zone-info-card">
					<h4><?php echo esc_html( $zone->name ); ?></h4>
					<p class="zone-description"><?php echo esc_html( $zone->description ); ?></p>
					<div class="zone-details">
						<div class="zone-detail">
							<strong><?php esc_html_e( 'Dimensions:', 'real-estate-banner-ads' ); ?></strong>
							<?php echo esc_html( $zone->dimensions ?: __( 'Flexible', 'real-estate-banner-ads' ) ); ?>
						</div>
						<div class="zone-detail">
							<strong><?php esc_html_e( 'Available Slots:', 'real-estate-banner-ads' ); ?></strong>
							<span class="<?php echo $available_slots === 0 ? 'slots-full' : 'slots-available'; ?>">
								<?php echo esc_html( $available_slots ); ?> / <?php echo esc_html( $zone->max_banners ); ?>
							</span>
						</div>
					</div>
				</div>
			<?php endforeach; ?>
		</div>
	</div>
</div>

<!-- JavaScript is handled by banner-ads-account.js -->
