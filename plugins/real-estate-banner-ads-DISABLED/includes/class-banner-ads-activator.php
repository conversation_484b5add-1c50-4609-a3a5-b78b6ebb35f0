<?php
/**
 * Fired during plugin activation
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/includes
 */

/**
 * Fired during plugin activation.
 *
 * This class defines all code necessary to run during the plugin's activation.
 */
class Banner_Ads_Activator {

	/**
	 * Activate the plugin.
	 *
	 * Create database tables, set default options, and flush rewrite rules.
	 */
	public static function activate() {
		self::create_database_tables();
		self::create_default_zones();
		self::create_banner_products();
		self::set_default_options();
		self::add_rewrite_endpoints();

		// Flush rewrite rules to ensure custom post types work
		flush_rewrite_rules();
	}

	/**
	 * Create custom database tables for analytics.
	 */
	private static function create_database_tables() {
		global $wpdb;

		$charset_collate = $wpdb->get_charset_collate();

		// Table for banner impressions
		$table_impressions = $wpdb->prefix . 'banner_impressions';
		$sql_impressions = "CREATE TABLE $table_impressions (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			campaign_id bigint(20) NOT NULL,
			user_id bigint(20) DEFAULT NULL,
			ip_address varchar(45) NOT NULL,
			user_agent text,
			page_url text,
			zone_id varchar(50),
			timestamp datetime DEFAULT CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			KEY campaign_id (campaign_id),
			KEY user_id (user_id),
			KEY timestamp (timestamp)
		) $charset_collate;";

		// Table for banner clicks
		$table_clicks = $wpdb->prefix . 'banner_clicks';
		$sql_clicks = "CREATE TABLE $table_clicks (
			id bigint(20) NOT NULL AUTO_INCREMENT,
			campaign_id bigint(20) NOT NULL,
			user_id bigint(20) DEFAULT NULL,
			ip_address varchar(45) NOT NULL,
			user_agent text,
			page_url text,
			zone_id varchar(50),
			timestamp datetime DEFAULT CURRENT_TIMESTAMP,
			PRIMARY KEY (id),
			KEY campaign_id (campaign_id),
			KEY user_id (user_id),
			KEY timestamp (timestamp)
		) $charset_collate;";

		require_once( ABSPATH . 'wp-admin/includes/upgrade.php' );
		dbDelta( $sql_impressions );
		dbDelta( $sql_clicks );
	}

	/**
	 * Create default ad zones.
	 */
	private static function create_default_zones() {
		$default_zones = array(
			'homepage-header' => array(
				'name' => 'Homepage Header',
				'description' => 'Banner area at the top of the homepage',
				'max_banners' => 3,
				'dimensions' => '728x90'
			),
			'sidebar' => array(
				'name' => 'Sidebar',
				'description' => 'Sidebar banner area',
				'max_banners' => 2,
				'dimensions' => '300x250'
			),
			'category-header' => array(
				'name' => 'Category Pages Header',
				'description' => 'Banner area on category pages',
				'max_banners' => 2,
				'dimensions' => '728x90'
			),
			'footer' => array(
				'name' => 'Footer',
				'description' => 'Footer banner area',
				'max_banners' => 4,
				'dimensions' => '300x100'
			)
		);

		update_option( 'banner_ads_zones', $default_zones );
	}

	/**
	 * Create WooCommerce products for banner packages.
	 */
	private static function create_banner_products() {
		if ( ! class_exists( 'WooCommerce' ) ) {
			return;
		}

		$packages = array(
			array(
				'name' => 'Banner Ad - 1 Week',
				'price' => 99.00,
				'duration' => 7,
				'sku' => 'banner-1-week'
			),
			array(
				'name' => 'Banner Ad - 1 Month',
				'price' => 299.00,
				'duration' => 30,
				'sku' => 'banner-1-month'
			),
			array(
				'name' => 'Banner Ad - 3 Months',
				'price' => 799.00,
				'duration' => 90,
				'sku' => 'banner-3-months'
			)
		);

		foreach ( $packages as $package ) {
			// Check if product already exists
			$existing_product = wc_get_product_id_by_sku( $package['sku'] );
			if ( $existing_product ) {
				continue;
			}

			$product = new WC_Product_Simple();
			$product->set_name( $package['name'] );
			$product->set_status( 'publish' );
			$product->set_catalog_visibility( 'visible' );
			$product->set_description( sprintf( 'Banner advertising package for %d days', $package['duration'] ) );
			$product->set_sku( $package['sku'] );
			$product->set_price( $package['price'] );
			$product->set_regular_price( $package['price'] );
			$product->set_manage_stock( false );
			$product->set_stock_status( 'instock' );
			$product->set_virtual( true );
			
			// Add custom meta for banner duration
			$product->update_meta_data( '_banner_duration_days', $package['duration'] );
			$product->update_meta_data( '_is_banner_product', 'yes' );
			
			$product->save();
		}
	}

	/**
	 * Add rewrite endpoints for account integration.
	 */
	private static function add_rewrite_endpoints() {
		add_rewrite_endpoint( 'banner-campaigns', EP_ROOT | EP_PAGES );
		add_rewrite_endpoint( 'upload-banner', EP_ROOT | EP_PAGES );
		add_rewrite_endpoint( 'banner-analytics', EP_ROOT | EP_PAGES );
	}

	/**
	 * Set default plugin options.
	 */
	private static function set_default_options() {
		$default_options = array(
			'banner_ads_version' => REAL_ESTATE_BANNER_ADS_VERSION,
			'banner_ads_db_version' => '1.0',
			'banner_ads_auto_approve' => 'no',
			'banner_ads_max_file_size' => 2048, // 2MB in KB
			'banner_ads_allowed_file_types' => array( 'jpg', 'jpeg', 'png', 'gif' ),
			'banner_ads_email_notifications' => 'yes'
		);

		foreach ( $default_options as $option_name => $option_value ) {
			if ( ! get_option( $option_name ) ) {
				add_option( $option_name, $option_value );
			}
		}
	}
}
