/**
 * Account Integration JavaScript for Banner Ads
 *
 * @package RealEstateBannerAds
 */

(function($) {
    'use strict';

    /**
     * Banner Ads Account Object
     */
    var BannerAdsAccount = {
        
        /**
         * Initialize account functionality
         */
        init: function() {
            this.initUploadForm();
            this.initAnalyticsFilters();
            this.initResponsiveTables();
        },

        /**
         * Initialize upload form functionality
         */
        initUploadForm: function() {
            // Banner type toggle
            $(document).on('click', '.banner-type-option', function() {
                $('.banner-type-option').removeClass('active');
                $(this).addClass('active');
                $(this).find('input[type="radio"]').prop('checked', true);

                var selectedType = $(this).find('input[type="radio"]').val();
                $('.conditional-fields').removeClass('active');
                $('.' + selectedType + '-fields').addClass('active');
            });

            // File upload handling - improved with better browser compatibility
            $(document).off('click', '#file-upload-area, #fallback-upload-btn').on('click', '#file-upload-area, #fallback-upload-btn', function(e) {
                console.log('File upload area clicked');
                e.preventDefault();
                e.stopPropagation();

                var fileInput = $('#banner_image')[0];
                console.log('File input found:', !!fileInput);

                if (fileInput) {
                    // Create a new file input if the current one is problematic
                    var newInput = document.createElement('input');
                    newInput.type = 'file';
                    newInput.accept = 'image/*';
                    newInput.style.position = 'absolute';
                    newInput.style.left = '-9999px';
                    newInput.style.top = '-9999px';
                    newInput.style.opacity = '0';

                    // Add to DOM temporarily
                    document.body.appendChild(newInput);

                    // Handle file selection
                    $(newInput).on('change', function() {
                        if (this.files && this.files[0]) {
                            // Transfer the file to the original input
                            var dt = new DataTransfer();
                            dt.items.add(this.files[0]);
                            fileInput.files = dt.files;

                            // Trigger change event on original input
                            $(fileInput).trigger('change');
                        }
                        // Clean up
                        document.body.removeChild(newInput);
                    });

                    // Trigger click on new input
                    newInput.click();
                } else {
                    console.error('File input not found');
                }
            });

            // Show fallback button after a delay if needed
            setTimeout(function() {
                if ($('#file-upload-area').length && $('#fallback-upload-btn').length) {
                    $('#fallback-upload-btn').show().css({
                        'display': 'inline-block',
                        'margin-top': '10px',
                        'padding': '10px 20px',
                        'background': '#0073aa',
                        'color': 'white',
                        'border': 'none',
                        'border-radius': '4px',
                        'cursor': 'pointer',
                        'font-size': '14px'
                    });
                }
            }, 1000);

            // Zone selection handling
            $(document).on('change', '#banner_zone', function() {
                var $option = $(this).find('option:selected');
                var $helpText = $('.zone-help-text');

                if ($option.val() && $option.data('available-slots') !== undefined) {
                    var availableSlots = $option.data('available-slots');
                    var maxBanners = $option.data('max-banners');
                    var dimensions = $option.data('dimensions');

                    var helpMessage = '';
                    if (dimensions) {
                        helpMessage += 'Recommended size: ' + dimensions + '. ';
                    }
                    helpMessage += availableSlots + ' of ' + maxBanners + ' slots available.';

                    $helpText.text(helpMessage).show();
                } else {
                    $helpText.hide();
                }
            });

            $(document).on('change', '#banner_image', function() {
                console.log('File input changed:', this.files);

                // Hide fallback button once file is selected
                $('#fallback-upload-btn').hide();

                var file = this.files[0];
                if (file) {
                    var fileSize = (file.size / 1024 / 1024).toFixed(2);
                    var maxSize = 2; // 2MB

                    // Validate file type
                    var allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
                    if (allowedTypes.indexOf(file.type) === -1) {
                        alert('Please select a valid image file (JPG, PNG, or GIF).');
                        $(this).val('');
                        return;
                    }

                    if (fileSize > maxSize) {
                        var errorMsg = 'File size must be less than ' + maxSize + 'MB';
                        if (typeof banner_ads_account !== 'undefined' && banner_ads_account.strings && banner_ads_account.strings.error) {
                            errorMsg = banner_ads_account.strings.error + ' ' + errorMsg;
                        }
                        alert(errorMsg);
                        $(this).val('');
                        return;
                    }

                    $('#file-upload-area').addClass('has-file');
                    $('#file-info').show();
                    $('#file-info .file-name').text(file.name);
                    $('#file-info .file-size').text(fileSize + ' MB');

                    // Show preview
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#file-info .file-preview').html('<img src="' + e.target.result + '" style="max-width: 200px; max-height: 100px; border-radius: 4px;">');
                    };
                    reader.readAsDataURL(file);
                } else {
                    // No file selected, reset UI
                    $('#file-upload-area').removeClass('has-file');
                    $('#file-info').hide();
                }
            });

            // Drag and drop
            $(document).on('dragover', '#file-upload-area', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            $(document).on('dragleave', '#file-upload-area', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            $(document).on('drop', '#file-upload-area', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                
                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    $('#banner_image')[0].files = files;
                    $('#banner_image').trigger('change');
                }
            });

            // Form submission with AJAX
            $(document).on('submit', '#banner-upload-form', function(e) {
                e.preventDefault();

                var $form = $(this);
                var $submitBtn = $('#submit-banner');
                var $spinner = $('.loading-spinner');

                // Validate form
                var bannerType = $('input[name="banner_type"]:checked').val();
                var selectedZone = $('#banner_zone').val();
                var isValid = true;
                var errorMessage = '';

                // Check if zone is selected and available
                if (!selectedZone) {
                    isValid = false;
                    errorMessage = 'Please select an ad zone.';
                } else {
                    var $selectedOption = $('#banner_zone option:selected');
                    if ($selectedOption.prop('disabled')) {
                        isValid = false;
                        errorMessage = 'The selected zone is full. Please choose a different zone.';
                    }
                }

                if (bannerType === 'image') {
                    var fileInput = $('#banner_image')[0];
                    if (!fileInput.files || fileInput.files.length === 0) {
                        isValid = false;
                        errorMessage = 'Please select an image file.';
                    }
                } else if (bannerType === 'html') {
                    var htmlContent = $('#html_content').val().trim();
                    if (!htmlContent) {
                        isValid = false;
                        errorMessage = 'Please enter HTML content.';
                    }
                }

                if (!isValid) {
                    BannerAdsAccount.showError(errorMessage);
                    return false;
                }

                // Show loading state
                $submitBtn.prop('disabled', true);
                $spinner.show();

                // Prepare form data
                var formData = new FormData(this);
                formData.append('action', 'upload_banner_campaign');
                formData.append('nonce', banner_ads_account.nonce);

                // Submit via AJAX
                $.ajax({
                    url: banner_ads_account.ajax_url,
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        if (response.success) {
                            BannerAdsAccount.showSuccess(response.data.message);
                            BannerAdsAccount.resetUploadForm();

                            // Redirect after a delay
                            if (response.data.redirect) {
                                setTimeout(function() {
                                    window.location.href = response.data.redirect;
                                }, 2000);
                            }
                        } else {
                            BannerAdsAccount.showError(response.data || 'An error occurred while submitting your campaign.');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX Error:', error);
                        BannerAdsAccount.showError('Network error. Please try again.');
                    },
                    complete: function() {
                        $submitBtn.prop('disabled', false);
                        $spinner.hide();
                    }
                });
            });
        },

        /**
         * Initialize analytics filters
         */
        initAnalyticsFilters: function() {
            // Auto-submit form on filter change
            $(document).on('change', '#date_range, #campaign_filter', function() {
                $(this).closest('form').submit();
            });
        },

        /**
         * Initialize responsive tables
         */
        initResponsiveTables: function() {
            // Add responsive classes to tables
            $('.shop_table').each(function() {
                if (!$(this).parent().hasClass('table-responsive')) {
                    $(this).wrap('<div class="table-responsive"></div>');
                }
            });
        },

        /**
         * Show success message
         */
        showSuccess: function(message) {
            $('#upload-response')
                .removeClass('woocommerce-error')
                .addClass('woocommerce-message')
                .html('<p>' + message + '</p>')
                .show();
            
            // Scroll to message
            $('html, body').animate({
                scrollTop: $('#upload-response').offset().top - 100
            }, 500);
        },

        /**
         * Show error message
         */
        showError: function(message) {
            $('#upload-response')
                .removeClass('woocommerce-message')
                .addClass('woocommerce-error')
                .html('<p>' + message + '</p>')
                .show();
            
            // Scroll to message
            $('html, body').animate({
                scrollTop: $('#upload-response').offset().top - 100
            }, 500);
        },

        /**
         * Reset upload form
         */
        resetUploadForm: function() {
            $('#banner-upload-form')[0].reset();
            $('#file-info').hide();
            $('#file-upload-area').removeClass('has-file');
            $('.conditional-fields').removeClass('active');
            $('.image-fields').addClass('active');
            $('.banner-type-option').removeClass('active');
            $('.banner-type-option:first').addClass('active');
            $('.banner-type-option:first input[type="radio"]').prop('checked', true);
        }
    };

    /**
     * Campaign Management
     */
    var CampaignManagement = {
        
        /**
         * Initialize campaign management
         */
        init: function() {
            this.initCampaignActions();
            this.initStatusUpdates();
        },

        /**
         * Initialize campaign actions
         */
        initCampaignActions: function() {
            // Campaign row hover effects
            $(document).on('mouseenter', '.campaign-row', function() {
                $(this).addClass('hover');
            });

            $(document).on('mouseleave', '.campaign-row', function() {
                $(this).removeClass('hover');
            });

            // Performance stats tooltips
            $(document).on('mouseenter', '.performance-stats .stat-item', function() {
                var $this = $(this);
                var label = $this.find('.stat-label').text();
                var value = $this.find('.stat-value').text();
                
                // Simple tooltip implementation
                if (!$this.find('.tooltip').length) {
                    $this.append('<div class="tooltip">' + label + ': ' + value + '</div>');
                }
            });

            $(document).on('mouseleave', '.performance-stats .stat-item', function() {
                $(this).find('.tooltip').remove();
            });
        },

        /**
         * Initialize status updates
         */
        initStatusUpdates: function() {
            // Status badge click handling (for future AJAX updates)
            $(document).on('click', '.status-badge', function(e) {
                // Placeholder for future status update functionality
                // Could open a modal or inline editor
            });
        }
    };

    /**
     * Analytics Dashboard
     */
    var AnalyticsDashboard = {
        
        /**
         * Initialize analytics dashboard
         */
        init: function() {
            this.initCharts();
            this.initStatBoxes();
        },

        /**
         * Initialize charts (placeholder for future chart library integration)
         */
        initCharts: function() {
            // Placeholder for Chart.js or similar integration
            $('.chart-container').each(function() {
                // Future chart implementation
            });
        },

        /**
         * Initialize stat boxes with animations
         */
        initStatBoxes: function() {
            // Animate stat numbers on page load
            $('.stat-number').each(function() {
                var $this = $(this);
                var finalValue = parseInt($this.text().replace(/,/g, ''));
                
                if (!isNaN(finalValue) && finalValue > 0) {
                    $this.text('0');
                    $({ value: 0 }).animate({ value: finalValue }, {
                        duration: 1000,
                        easing: 'swing',
                        step: function() {
                            $this.text(Math.floor(this.value).toLocaleString());
                        },
                        complete: function() {
                            $this.text(finalValue.toLocaleString());
                        }
                    });
                }
            });
        }
    };

    /**
     * Utility Functions
     */
    var Utils = {
        
        /**
         * Format numbers with commas
         */
        formatNumber: function(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        },

        /**
         * Debounce function
         */
        debounce: function(func, wait, immediate) {
            var timeout;
            return function() {
                var context = this, args = arguments;
                var later = function() {
                    timeout = null;
                    if (!immediate) func.apply(context, args);
                };
                var callNow = immediate && !timeout;
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
                if (callNow) func.apply(context, args);
            };
        },

        /**
         * Show loading state
         */
        showLoading: function(element) {
            $(element).addClass('loading').prop('disabled', true);
        },

        /**
         * Hide loading state
         */
        hideLoading: function(element) {
            $(element).removeClass('loading').prop('disabled', false);
        }
    };

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        console.log('Banner Ads Account JS: Document ready');
        console.log('Upload banner page found:', $('.woocommerce-upload-banner').length);
        console.log('File upload area found:', $('#file-upload-area').length);
        console.log('Banner image input found:', $('#banner_image').length);
        console.log('Upload form found:', $('#banner-upload-form').length);
        console.log('banner_ads_account object:', typeof banner_ads_account !== 'undefined' ? banner_ads_account : 'Not available');

        // Check if we're on an account page or upload page
        if ($('.woocommerce-account').length || $('.woocommerce-banner-campaigns').length || $('.woocommerce-upload-banner').length) {
            BannerAdsAccount.init();
            CampaignManagement.init();
            AnalyticsDashboard.init();

            console.log('Banner Ads Account initialized');
        }
    });

    // Make objects available globally for debugging
    window.BannerAdsAccount = BannerAdsAccount;
    window.CampaignManagement = CampaignManagement;
    window.AnalyticsDashboard = AnalyticsDashboard;

})(jQuery);
