<?php
/**
 * WordPress/WooCommerce Account Integration
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/includes
 */

/**
 * Account Integration class.
 *
 * Integrates banner ads management into WordPress/WooCommerce account dashboard.
 */
class Banner_Ads_Account_Integration {

	/**
	 * The ID of this plugin.
	 *
	 * @var string $plugin_name The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @var string $version The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @param string $plugin_name The name of the plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;
	}

	/**
	 * Initialize account integration.
	 */
	public function init() {
		// WooCommerce account integration
		if ( class_exists( 'WooCommerce' ) ) {
			add_filter( 'woocommerce_account_menu_items', array( $this, 'add_account_menu_items' ) );
			add_action( 'woocommerce_account_banner-campaigns_endpoint', array( $this, 'banner_campaigns_content' ) );
			add_action( 'woocommerce_account_upload-banner_endpoint', array( $this, 'upload_banner_content' ) );
			add_action( 'woocommerce_account_banner-analytics_endpoint', array( $this, 'banner_analytics_content' ) );
			add_action( 'init', array( $this, 'add_account_endpoints' ) );
			add_filter( 'woocommerce_account_menu_item_classes', array( $this, 'add_menu_item_classes' ), 10, 2 );
		}

		// WordPress native account integration (fallback)
		add_action( 'show_user_profile', array( $this, 'add_user_profile_fields' ) );
		add_action( 'edit_user_profile', array( $this, 'add_user_profile_fields' ) );
		
		// Handle form submissions
		add_action( 'wp_ajax_upload_banner_campaign', array( $this, 'handle_banner_upload' ) );
		add_action( 'wp_ajax_nopriv_upload_banner_campaign', array( $this, 'handle_banner_upload' ) );
		
		// Enqueue scripts for account pages
		add_action( 'wp_enqueue_scripts', array( $this, 'enqueue_account_scripts' ) );
	}

	/**
	 * Add WooCommerce account endpoints.
	 */
	public function add_account_endpoints() {
		add_rewrite_endpoint( 'banner-campaigns', EP_ROOT | EP_PAGES );
		add_rewrite_endpoint( 'upload-banner', EP_ROOT | EP_PAGES );
		add_rewrite_endpoint( 'banner-analytics', EP_ROOT | EP_PAGES );
	}

	/**
	 * Add menu items to WooCommerce account menu.
	 *
	 * @param array $items Existing menu items.
	 * @return array Modified menu items.
	 */
	public function add_account_menu_items( $items ) {
		// Insert banner ads items before logout
		$logout = $items['customer-logout'];
		unset( $items['customer-logout'] );

		$items['banner-campaigns'] = __( 'My Banner Campaigns', 'real-estate-banner-ads' );
		$items['upload-banner'] = __( 'Upload Banner', 'real-estate-banner-ads' );
		$items['banner-analytics'] = __( 'Banner Analytics', 'real-estate-banner-ads' );
		$items['customer-logout'] = $logout;

		return $items;
	}

	/**
	 * Add CSS classes to menu items.
	 *
	 * @param array  $classes Menu item classes.
	 * @param string $endpoint Current endpoint.
	 * @return array Modified classes.
	 */
	public function add_menu_item_classes( $classes, $endpoint ) {
		if ( in_array( $endpoint, array( 'banner-campaigns', 'upload-banner', 'banner-analytics' ) ) ) {
			$classes[] = 'banner-ads-menu-item';
		}
		return $classes;
	}

	/**
	 * Banner campaigns content for account page.
	 */
	public function banner_campaigns_content() {
		$user_id = get_current_user_id();
		$user = wp_get_current_user();

		// Get user's campaigns
		$user_campaigns = get_posts( array(
			'post_type' => 'banner_campaign',
			'author' => $user_id,
			'posts_per_page' => -1,
			'post_status' => 'publish',
			'orderby' => 'date',
			'order' => 'DESC'
		));

		// Calculate summary stats
		$total_campaigns = count( $user_campaigns );
		$active_campaigns = 0;
		$total_impressions = 0;
		$total_clicks = 0;

		foreach ( $user_campaigns as $campaign_post ) {
			$status = get_post_meta( $campaign_post->ID, '_campaign_status', true );
			if ( $status === 'active' ) {
				$active_campaigns++;
			}
			
			$impressions = Banner_Analytics::get_impressions( $campaign_post->ID );
			$clicks = Banner_Analytics::get_clicks( $campaign_post->ID );
			
			$total_impressions += $impressions;
			$total_clicks += $clicks;
		}

		$average_ctr = $total_impressions > 0 ? round( ( $total_clicks / $total_impressions ) * 100, 2 ) : 0;

		include REAL_ESTATE_BANNER_ADS_PATH . 'includes/account-templates/banner-campaigns.php';
	}

	/**
	 * Upload banner content for account page.
	 */
	public function upload_banner_content() {
		include REAL_ESTATE_BANNER_ADS_PATH . 'includes/account-templates/upload-banner.php';
	}

	/**
	 * Banner analytics content for account page.
	 */
	public function banner_analytics_content() {
		$user_id = get_current_user_id();
		
		// Get date range from request
		$date_range = $_GET['date_range'] ?? '30days';
		$campaign_filter = $_GET['campaign_filter'] ?? '';

		// Calculate date range
		$end_date = date( 'Y-m-d' );
		switch ( $date_range ) {
			case '7days':
				$start_date = date( 'Y-m-d', strtotime( '-7 days' ) );
				break;
			case '30days':
				$start_date = date( 'Y-m-d', strtotime( '-30 days' ) );
				break;
			case '90days':
				$start_date = date( 'Y-m-d', strtotime( '-90 days' ) );
				break;
			default:
				$start_date = date( 'Y-m-d', strtotime( '-30 days' ) );
		}

		// Get user's campaigns
		$user_campaigns = get_posts( array(
			'post_type' => 'banner_campaign',
			'author' => $user_id,
			'posts_per_page' => -1,
			'post_status' => 'publish',
			'fields' => 'ids'
		));

		// Get analytics data
		if ( $campaign_filter && in_array( $campaign_filter, $user_campaigns ) ) {
			$campaign_ids = array( intval( $campaign_filter ) );
		} else {
			$campaign_ids = $user_campaigns;
		}

		$analytics_summary = Banner_Analytics::get_campaigns_summary( $campaign_ids, $start_date, $end_date );

		include REAL_ESTATE_BANNER_ADS_PATH . 'includes/account-templates/banner-analytics.php';
	}

	/**
	 * Handle banner upload via AJAX.
	 */
	public function handle_banner_upload() {
		// Debug logging
		error_log( 'Banner upload AJAX handler called' );
		error_log( 'POST data: ' . print_r( $_POST, true ) );

		check_ajax_referer( 'banner_upload_nonce', 'nonce' );

		if ( ! is_user_logged_in() ) {
			error_log( 'Banner upload failed: User not logged in' );
			wp_send_json_error( __( 'You must be logged in to upload banners.', 'real-estate-banner-ads' ) );
		}

		$user_id = get_current_user_id();

		// Validate and process form data
		$form_data = array(
			'title' => sanitize_text_field( $_POST['campaign_title'] ),
			'banner_type' => sanitize_text_field( $_POST['banner_type'] ),
			'zone' => sanitize_text_field( $_POST['banner_zone'] ),
			'user_id' => $user_id,
			'link_url' => esc_url_raw( $_POST['link_url'] ),
			'link_target' => sanitize_text_field( $_POST['link_target'] )
		);

		if ( $form_data['banner_type'] === 'image' ) {
			// Handle image upload
			if ( isset( $_FILES['banner_image'] ) && $_FILES['banner_image']['error'] === UPLOAD_ERR_OK ) {
				$upload_result = wp_handle_upload( $_FILES['banner_image'], array( 'test_form' => false ) );
				
				if ( ! isset( $upload_result['error'] ) ) {
					$form_data['image_url'] = $upload_result['url'];
					$form_data['alt_text'] = sanitize_text_field( $_POST['alt_text'] );
				} else {
					wp_send_json_error( $upload_result['error'] );
				}
			} else {
				wp_send_json_error( __( 'Please select an image file to upload.', 'real-estate-banner-ads' ) );
			}
		} else {
			$form_data['html_content'] = wp_kses_post( $_POST['html_content'] );
		}

		error_log( 'Creating campaign with data: ' . print_r( $form_data, true ) );

		$campaign = Banner_Campaign::create_from_form( $form_data );

		if ( is_wp_error( $campaign ) ) {
			error_log( 'Campaign creation failed: ' . $campaign->get_error_message() );
			wp_send_json_error( $campaign->get_error_message() );
		}

		error_log( 'Campaign created successfully with ID: ' . $campaign->id );

		// If admin, automatically activate the campaign
		if ( current_user_can( 'manage_options' ) ) {
			$campaign->status = 'active';
			$campaign->start_date = current_time( 'Y-m-d H:i:s' );
			$campaign->end_date = date( 'Y-m-d H:i:s', strtotime( '+30 days' ) );
			$campaign->save();
			
			$message = __( 'Your banner campaign has been created and activated successfully! As an admin, your campaign is live immediately.', 'real-estate-banner-ads' );
		} else {
			$message = __( 'Your banner campaign has been submitted successfully! It will be reviewed and activated once you complete your purchase.', 'real-estate-banner-ads' );
		}

		wp_send_json_success( array(
			'message' => $message,
			'campaign_id' => $campaign->id,
			'redirect' => wc_get_account_endpoint_url( 'banner-campaigns' )
		));
	}

	/**
	 * Add user profile fields (WordPress native fallback).
	 */
	public function add_user_profile_fields( $user ) {
		if ( ! current_user_can( 'edit_user', $user->ID ) ) {
			return;
		}

		$user_campaigns = get_posts( array(
			'post_type' => 'banner_campaign',
			'author' => $user->ID,
			'posts_per_page' => 5,
			'post_status' => 'publish'
		));
		?>
		<h3><?php esc_html_e( 'Banner Campaigns', 'real-estate-banner-ads' ); ?></h3>
		<table class="form-table">
			<tr>
				<th><label><?php esc_html_e( 'Recent Campaigns', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<?php if ( ! empty( $user_campaigns ) ) : ?>
						<ul>
							<?php foreach ( $user_campaigns as $campaign ) : ?>
								<?php $status = get_post_meta( $campaign->ID, '_campaign_status', true ); ?>
								<li>
									<strong><?php echo esc_html( $campaign->post_title ); ?></strong> 
									- <span class="status-<?php echo esc_attr( $status ); ?>"><?php echo esc_html( ucfirst( $status ) ); ?></span>
									<?php if ( current_user_can( 'manage_options' ) ) : ?>
										<a href="<?php echo esc_url( admin_url( 'post.php?post=' . $campaign->ID . '&action=edit' ) ); ?>"><?php esc_html_e( 'Edit', 'real-estate-banner-ads' ); ?></a>
									<?php endif; ?>
								</li>
							<?php endforeach; ?>
						</ul>
					<?php else : ?>
						<p><?php esc_html_e( 'No banner campaigns found.', 'real-estate-banner-ads' ); ?></p>
					<?php endif; ?>
					
					<?php if ( class_exists( 'WooCommerce' ) ) : ?>
						<p><a href="<?php echo esc_url( wc_get_account_endpoint_url( 'banner-campaigns' ) ); ?>" class="button">
							<?php esc_html_e( 'Manage Banner Campaigns', 'real-estate-banner-ads' ); ?>
						</a></p>
					<?php endif; ?>
				</td>
			</tr>
		</table>
		<?php
	}

	/**
	 * Enqueue scripts for account pages.
	 */
	public function enqueue_account_scripts() {
		// Always enqueue on account pages and when user is logged in
		$should_enqueue = false;

		if ( function_exists( 'is_account_page' ) && is_account_page() ) {
			$should_enqueue = true;
		} elseif ( is_user_logged_in() && ( is_page() || is_single() ) ) {
			$should_enqueue = true;
		}

		if ( $should_enqueue ) {
			wp_enqueue_script( 'banner-ads-account', REAL_ESTATE_BANNER_ADS_URL . 'includes/js/banner-ads-account.js', array( 'jquery' ), $this->version, true );
			wp_enqueue_style( 'banner-ads-account', REAL_ESTATE_BANNER_ADS_URL . 'includes/css/banner-ads-account.css', array(), $this->version );

			wp_localize_script( 'banner-ads-account', 'banner_ads_account', array(
				'ajax_url' => admin_url( 'admin-ajax.php' ),
				'nonce' => wp_create_nonce( 'banner_upload_nonce' ),
				'debug' => defined( 'WP_DEBUG' ) && WP_DEBUG,
				'strings' => array(
					'uploading' => __( 'Uploading...', 'real-estate-banner-ads' ),
					'success' => __( 'Success!', 'real-estate-banner-ads' ),
					'error' => __( 'Error:', 'real-estate-banner-ads' )
				)
			));
		}
	}
}
