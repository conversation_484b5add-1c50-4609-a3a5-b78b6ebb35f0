<?php
/**
 * The file that defines the core plugin class
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/includes
 */

/**
 * The core plugin class.
 *
 * This is used to define internationalization, admin-specific hooks, and
 * public-facing site hooks.
 */
class Banner_Ads {

	/**
	 * The loader that's responsible for maintaining and registering all hooks that power
	 * the plugin.
	 *
	 * @var Banner_Ads_Loader $loader Maintains and registers all hooks for the plugin.
	 */
	protected $loader;

	/**
	 * The unique identifier of this plugin.
	 *
	 * @var string $plugin_name The string used to uniquely identify this plugin.
	 */
	protected $plugin_name;

	/**
	 * The current version of the plugin.
	 *
	 * @var string $version The current version of the plugin.
	 */
	protected $version;

	/**
	 * Define the core functionality of the plugin.
	 */
	public function __construct() {
		if ( defined( 'REAL_ESTATE_BANNER_ADS_VERSION' ) ) {
			$this->version = REAL_ESTATE_BANNER_ADS_VERSION;
		} else {
			$this->version = '1.0.0';
		}
		$this->plugin_name = 'real-estate-banner-ads';

		$this->load_dependencies();
		$this->set_locale();
		$this->define_admin_hooks();
		$this->define_public_hooks();
		$this->define_woocommerce_hooks();
		$this->define_account_integration_hooks();
	}

	/**
	 * Load the required dependencies for this plugin.
	 */
	private function load_dependencies() {
		/**
		 * The class responsible for orchestrating the actions and filters of the
		 * core plugin.
		 */
		require_once REAL_ESTATE_BANNER_ADS_PATH . 'includes/class-banner-ads-loader.php';

		/**
		 * The class responsible for defining internationalization functionality
		 * of the plugin.
		 */
		require_once REAL_ESTATE_BANNER_ADS_PATH . 'includes/class-banner-ads-i18n.php';

		/**
		 * The class responsible for defining all actions that occur in the admin area.
		 */
		require_once REAL_ESTATE_BANNER_ADS_PATH . 'admin/class-banner-ads-admin.php';

		/**
		 * The class responsible for defining all actions that occur in the public-facing
		 * side of the site.
		 */
		require_once REAL_ESTATE_BANNER_ADS_PATH . 'frontend/class-banner-ads-frontend.php';

		/**
		 * The class responsible for WooCommerce integration.
		 */
		require_once REAL_ESTATE_BANNER_ADS_PATH . 'woocommerce/class-banner-ads-woocommerce.php';

		/**
		 * Model classes for data handling.
		 */
		require_once REAL_ESTATE_BANNER_ADS_PATH . 'models/class-banner-campaign.php';
		require_once REAL_ESTATE_BANNER_ADS_PATH . 'models/class-banner-zone.php';
		require_once REAL_ESTATE_BANNER_ADS_PATH . 'models/class-banner-analytics.php';

		/**
		 * The class responsible for WordPress/WooCommerce account integration.
		 */
		require_once REAL_ESTATE_BANNER_ADS_PATH . 'includes/class-banner-ads-account-integration.php';

		$this->loader = new Banner_Ads_Loader();
	}

	/**
	 * Define the locale for this plugin for internationalization.
	 */
	private function set_locale() {
		$plugin_i18n = new Banner_Ads_i18n();
		$this->loader->add_action( 'plugins_loaded', $plugin_i18n, 'load_plugin_textdomain' );
	}

	/**
	 * Register all of the hooks related to the admin area functionality
	 * of the plugin.
	 */
	private function define_admin_hooks() {
		$plugin_admin = new Banner_Ads_Admin( $this->get_plugin_name(), $this->get_version() );

		$this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_styles' );
		$this->loader->add_action( 'admin_enqueue_scripts', $plugin_admin, 'enqueue_scripts' );
		$this->loader->add_action( 'admin_menu', $plugin_admin, 'add_admin_menu' );
		$this->loader->add_action( 'init', $plugin_admin, 'register_post_types' );
		$this->loader->add_action( 'init', $plugin_admin, 'register_taxonomies' );
	}

	/**
	 * Register all of the hooks related to the public-facing functionality
	 * of the plugin.
	 */
	private function define_public_hooks() {
		$plugin_public = new Banner_Ads_Frontend( $this->get_plugin_name(), $this->get_version() );

		$this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_styles' );
		$this->loader->add_action( 'wp_enqueue_scripts', $plugin_public, 'enqueue_scripts' );
		$this->loader->add_action( 'init', $plugin_public, 'register_shortcodes' );
		$this->loader->add_action( 'wp_ajax_track_banner_impression', $plugin_public, 'track_banner_impression' );
		$this->loader->add_action( 'wp_ajax_nopriv_track_banner_impression', $plugin_public, 'track_banner_impression' );
		$this->loader->add_action( 'wp_ajax_track_banner_click', $plugin_public, 'track_banner_click' );
		$this->loader->add_action( 'wp_ajax_nopriv_track_banner_click', $plugin_public, 'track_banner_click' );
	}

	/**
	 * Register all of the hooks related to WooCommerce integration.
	 */
	private function define_woocommerce_hooks() {
		$woocommerce_integration = new Banner_Ads_WooCommerce( $this->get_plugin_name(), $this->get_version() );

		$this->loader->add_action( 'woocommerce_order_status_completed', $woocommerce_integration, 'activate_banner_campaign' );
		$this->loader->add_action( 'woocommerce_order_status_processing', $woocommerce_integration, 'process_banner_order' );
		$this->loader->add_action( 'init', $woocommerce_integration, 'create_banner_products' );
	}

	/**
	 * Register all of the hooks related to account integration.
	 */
	private function define_account_integration_hooks() {
		$account_integration = new Banner_Ads_Account_Integration( $this->get_plugin_name(), $this->get_version() );
		$account_integration->init();
	}

	/**
	 * Run the loader to execute all of the hooks with WordPress.
	 */
	public function run() {
		$this->loader->run();
	}

	/**
	 * The name of the plugin used to uniquely identify it within the context of
	 * WordPress and to define internationalization functionality.
	 *
	 * @return string The name of the plugin.
	 */
	public function get_plugin_name() {
		return $this->plugin_name;
	}

	/**
	 * The reference to the class that orchestrates the hooks with the plugin.
	 *
	 * @return Banner_Ads_Loader Orchestrates the hooks of the plugin.
	 */
	public function get_loader() {
		return $this->loader;
	}

	/**
	 * Retrieve the version number of the plugin.
	 *
	 * @return string The version number of the plugin.
	 */
	public function get_version() {
		return $this->version;
	}
}
