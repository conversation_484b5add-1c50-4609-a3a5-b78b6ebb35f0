/**
 * Account Integration CSS for Banner Ads
 *
 * @package RealEstateBannerAds
 */

/* Account Menu Items */
.woocommerce-account .woocommerce-MyAccount-navigation ul li.banner-ads-menu-item a {
	position: relative;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.banner-ads-menu-item a:before {
	content: "📊";
	margin-right: 8px;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--banner-campaigns a:before {
	content: "📋";
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--upload-banner a:before {
	content: "📤";
}

.woocommerce-account .woocommerce-MyAccount-navigation ul li.woocommerce-MyAccount-navigation-link--banner-analytics a:before {
	content: "📊";
}

/* Summary Stats */
.summary-stats {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
	gap: 20px;
	margin: 20px 0;
}

.stat-box {
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 6px;
	padding: 20px;
	text-align: center;
	transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-box:hover {
	transform: translateY(-2px);
	box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.stat-number {
	font-size: 28px;
	font-weight: bold;
	color: #0073aa;
	display: block;
	margin-bottom: 5px;
}

.stat-label {
	font-size: 12px;
	color: #666;
	text-transform: uppercase;
	letter-spacing: 0.5px;
}

/* Campaign Actions */
.banner-campaigns-actions {
	margin: 20px 0;
	display: flex;
	gap: 10px;
	flex-wrap: wrap;
}

.banner-campaigns-actions .button {
	margin: 0;
}

/* Campaign Table */
.banner_campaigns_table {
	margin-top: 20px;
}

.campaign-thumbnail {
	width: 80px;
	height: 40px;
	object-fit: cover;
	border-radius: 4px;
	border: 1px solid #ddd;
}

.campaign-thumbnail.html-banner {
	display: flex;
	align-items: center;
	justify-content: center;
	background: #f0f0f0;
	color: #666;
	font-size: 12px;
	font-weight: bold;
}

.campaign-meta {
	margin-top: 5px;
}

.campaign-meta small {
	color: #666;
	font-size: 11px;
}

.status-badge {
	padding: 4px 8px;
	border-radius: 3px;
	font-size: 11px;
	font-weight: bold;
	text-transform: uppercase;
	display: inline-block;
}

.status-badge.status-pending {
	background: #fff3cd;
	color: #856404;
	border: 1px solid #ffeaa7;
}

.status-badge.status-approved {
	background: #d1ecf1;
	color: #0c5460;
	border: 1px solid #bee5eb;
}

.status-badge.status-active {
	background: #d4edda;
	color: #155724;
	border: 1px solid #c3e6cb;
}

.status-badge.status-expired {
	background: #f8d7da;
	color: #721c24;
	border: 1px solid #f5c6cb;
}

.status-badge.status-rejected {
	background: #f8d7da;
	color: #721c24;
	border: 1px solid #f5c6cb;
}

.status-badge.status-paused {
	background: #e2e3e5;
	color: #383d41;
	border: 1px solid #d6d8db;
}

.date-range div {
	margin-bottom: 3px;
	font-size: 13px;
}

.no-dates {
	color: #999;
	font-style: italic;
}

.performance-stats {
	display: flex;
	gap: 15px;
	flex-wrap: wrap;
}

.performance-stats .stat-item {
	text-align: center;
	min-width: 50px;
}

.performance-stats .stat-value {
	display: block;
	font-weight: bold;
	color: #0073aa;
	font-size: 14px;
}

.performance-stats .stat-label {
	font-size: 10px;
	color: #666;
	text-transform: uppercase;
}

/* Upload Form */
.banner-type-selection {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 15px;
	margin-top: 10px;
}

.banner-type-option {
	border: 2px solid #ddd;
	border-radius: 6px;
	padding: 15px;
	cursor: pointer;
	transition: all 0.3s ease;
	display: block;
	text-align: center;
}

.banner-type-option:hover {
	border-color: #0073aa;
}

.banner-type-option.active {
	border-color: #0073aa;
	background: #f0f8ff;
}

.banner-type-option input[type="radio"] {
	display: none;
}

.option-content strong {
	display: block;
	margin-bottom: 5px;
	color: #333;
}

.option-content small {
	color: #666;
	font-size: 12px;
}

.conditional-fields {
	display: none;
}

.conditional-fields.active {
	display: block;
}

/* File Upload Area */
.file-upload-area {
	border: 2px dashed #ddd;
	border-radius: 6px;
	padding: 40px 20px;
	text-align: center;
	transition: all 0.3s ease;
	cursor: pointer;
	margin-top: 10px;
	position: relative;
	user-select: none;
}

.file-upload-area:hover,
.file-upload-area.dragover {
	border-color: #0073aa;
	background: #f0f8ff;
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
}

.file-upload-area.has-file {
	border-color: #28a745;
	background: #f8fff9;
}

.file-upload-area:active {
	transform: translateY(0);
}

.upload-icon {
	font-size: 48px;
	margin-bottom: 10px;
}

.upload-text {
	margin-bottom: 10px;
}

.upload-text strong {
	color: #0073aa;
}

.upload-requirements {
	color: #666;
	font-size: 12px;
}

.file-info {
	margin-top: 15px;
	padding: 15px;
	background: #f8f9fa;
	border-radius: 6px;
	border: 1px solid #e9ecef;
}

.file-details {
	margin-top: 10px;
}

.file-name {
	font-weight: bold;
	color: #333;
}

.file-size {
	color: #666;
	font-size: 12px;
}

/* Fallback Upload Button */
#fallback-upload-btn {
	background: #0073aa !important;
	color: white !important;
	border: none !important;
	border-radius: 4px !important;
	padding: 12px 24px !important;
	font-size: 14px !important;
	cursor: pointer !important;
	transition: all 0.3s ease !important;
	margin-top: 15px !important;
}

#fallback-upload-btn:hover {
	background: #005a87 !important;
	transform: translateY(-1px);
	box-shadow: 0 2px 8px rgba(0, 115, 170, 0.3);
}

#fallback-upload-btn:active {
	transform: translateY(0);
}

/* Zone Selection */
#banner_zone option:disabled {
	color: #999 !important;
	background-color: #f5f5f5 !important;
	font-style: italic;
}

.zone-help-text {
	font-size: 12px;
	color: #666;
	margin-top: 5px;
	padding: 5px 10px;
	background: #f8f9fa;
	border-radius: 3px;
	border-left: 3px solid #0073aa;
}

/* Form validation messages */
.form-row.has-error select,
.form-row.has-error input {
	border-color: #dc3545 !important;
	box-shadow: 0 0 0 1px rgba(220, 53, 69, 0.25);
}

.form-row.has-error .zone-help-text {
	color: #dc3545;
	background: #f8d7da;
	border-left-color: #dc3545;
}

/* Loading States */
.loading-spinner {
	margin-left: 10px;
	color: #666;
	font-style: italic;
}

/* Next Steps */
.next-steps-info {
	margin-top: 30px;
	padding: 20px;
	background: #f8f9fa;
	border-radius: 6px;
	border-left: 4px solid #0073aa;
}

.next-steps-info h3 {
	margin-top: 0;
	color: #333;
}

.next-steps-info ol {
	margin: 15px 0;
	padding-left: 20px;
}

.next-steps-info li {
	margin-bottom: 8px;
	color: #666;
}

/* Zone Information */
.zone-information {
	margin-top: 30px;
}

.zones-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 15px;
	margin-top: 15px;
}

.zone-info-card {
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 6px;
	padding: 15px;
}

.zone-info-card h4 {
	margin: 0 0 10px 0;
	color: #333;
}

.zone-description {
	color: #666;
	font-size: 14px;
	margin-bottom: 10px;
}

.zone-details {
	font-size: 13px;
}

.zone-detail {
	margin-bottom: 5px;
}

.slots-available {
	color: #28a745;
	font-weight: bold;
}

.slots-full {
	color: #dc3545;
	font-weight: bold;
}

/* Analytics */
.analytics-filters {
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 6px;
	padding: 20px;
	margin-bottom: 20px;
}

.filter-row {
	display: flex;
	gap: 20px;
	align-items: end;
	flex-wrap: wrap;
}

.filter-group {
	display: flex;
	flex-direction: column;
	gap: 5px;
}

.filter-group label {
	font-weight: bold;
	color: #333;
	font-size: 14px;
}

.performance-table-wrapper {
	overflow-x: auto;
}

.campaign_performance_table {
	margin-top: 15px;
}

/* Performance Tips */
.performance-tips {
	margin-top: 30px;
}

.tips-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
	gap: 20px;
	margin-top: 15px;
}

.tip-card {
	background: #f8f9fa;
	border: 1px solid #e9ecef;
	border-radius: 6px;
	padding: 20px;
}

.tip-card h4 {
	margin: 0 0 10px 0;
	color: #0073aa;
}

.tip-card p {
	margin: 0;
	color: #666;
	font-size: 14px;
	line-height: 1.5;
}

.analytics-actions {
	margin-top: 30px;
	display: flex;
	gap: 10px;
	flex-wrap: wrap;
}

/* Responsive Design */
@media (max-width: 768px) {
	.summary-stats {
		grid-template-columns: repeat(2, 1fr);
		gap: 10px;
	}
	
	.stat-box {
		padding: 15px;
	}
	
	.stat-number {
		font-size: 24px;
	}
	
	.banner-campaigns-actions {
		flex-direction: column;
	}
	
	.banner-campaigns-actions .button {
		width: 100%;
		text-align: center;
	}
	
	.banner-type-selection {
		grid-template-columns: 1fr;
	}
	
	.performance-stats {
		justify-content: space-around;
	}
	
	.filter-row {
		flex-direction: column;
		align-items: stretch;
	}
	
	.filter-group {
		width: 100%;
	}
	
	.tips-grid {
		grid-template-columns: 1fr;
	}
	
	.zones-grid {
		grid-template-columns: 1fr;
	}
}
