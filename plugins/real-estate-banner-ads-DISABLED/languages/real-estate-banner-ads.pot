# Copyright (C) 2024 Real Estate Banner Ads Manager
# This file is distributed under the same license as the Real Estate Banner Ads Manager package.
msgid ""
msgstr ""
"Project-Id-Version: Real Estate Banner Ads Manager 1.0.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/real-estate-banner-ads\n"
"POT-Creation-Date: 2024-06-26 12:00:00+00:00\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"PO-Revision-Date: 2024-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"

#: real-estate-banner-ads.php:67
msgid "Real Estate Banner Ads Manager requires WooCommerce to be installed and active."
msgstr ""

#: admin/class-banner-ads-admin.php:67
msgid "Banner Ads"
msgstr ""

#: admin/class-banner-ads-admin.php:77
msgid "All Campaigns"
msgstr ""

#: admin/class-banner-ads-admin.php:86
msgid "Ad Zones"
msgstr ""

#: admin/class-banner-ads-admin.php:95
msgid "Analytics"
msgstr ""

#: admin/class-banner-ads-admin.php:104
msgid "Settings"
msgstr ""

#: admin/class-banner-ads-admin.php:118
msgctxt "Post type general name"
msgid "Banner Campaigns"
msgstr ""

#: admin/class-banner-ads-admin.php:119
msgctxt "Post type singular name"
msgid "Banner Campaign"
msgstr ""

#: admin/class-banner-ads-admin.php:120
msgctxt "Admin Menu text"
msgid "Banner Campaigns"
msgstr ""

#: admin/class-banner-ads-admin.php:121
msgctxt "Add New on Toolbar"
msgid "Banner Campaign"
msgstr ""

#: admin/class-banner-ads-admin.php:122
msgid "Add New"
msgstr ""

#: admin/class-banner-ads-admin.php:123
msgid "Add New Banner Campaign"
msgstr ""

#: admin/class-banner-ads-admin.php:124
msgid "New Banner Campaign"
msgstr ""

#: admin/class-banner-ads-admin.php:125
msgid "Edit Banner Campaign"
msgstr ""

#: admin/class-banner-ads-admin.php:126
msgid "View Banner Campaign"
msgstr ""

#: admin/class-banner-ads-admin.php:127
msgid "All Banner Campaigns"
msgstr ""

#: admin/class-banner-ads-admin.php:128
msgid "Search Banner Campaigns"
msgstr ""

#: admin/class-banner-ads-admin.php:129
msgid "Parent Banner Campaigns:"
msgstr ""

#: admin/class-banner-ads-admin.php:130
msgid "No banner campaigns found."
msgstr ""

#: admin/class-banner-ads-admin.php:131
msgid "No banner campaigns found in Trash."
msgstr ""

#: frontend/class-banner-ads-frontend.php:89
msgid "Please log in to access your banner ads dashboard."
msgstr ""

#: frontend/class-banner-ads-frontend.php:104
msgid "Please log in to upload banner ads."
msgstr ""

#: models/class-banner-campaign.php:234
msgid "Missing required field: %s"
msgstr ""

#: models/class-banner-campaign.php:240
msgid "Invalid banner type"
msgstr ""

#: models/class-banner-campaign.php:245
msgid "Invalid ad zone"
msgstr ""

#: models/class-banner-campaign.php:258
msgid "Image URL is required for image banners"
msgstr ""

#: models/class-banner-campaign.php:264
msgid "HTML content is required for HTML banners"
msgstr ""

#: models/class-banner-zone.php:154
msgid "Full"
msgstr ""

#: models/class-banner-zone.php:156
msgid "slots available"
msgstr ""

#: models/class-banner-zone.php:175
msgid "Missing required field: %s"
msgstr ""

#: models/class-banner-zone.php:181
msgid "Zone ID can only contain lowercase letters, numbers, hyphens, and underscores"
msgstr ""

#: models/class-banner-zone.php:187
msgid "A zone with this ID already exists"
msgstr ""

#: models/class-banner-zone.php:200
msgid "Failed to save zone"
msgstr ""

#: models/class-banner-zone.php:213
msgid "Zone not found"
msgstr ""

#: models/class-banner-zone.php:235
msgid "Failed to update zone"
msgstr ""

#: woocommerce/class-banner-ads-woocommerce.php:189
msgid "Banner Ad Order Received - Next Steps"
msgstr ""

#: woocommerce/class-banner-ads-woocommerce.php:191
msgid "Hi %s,\n\nThank you for your banner ad order (#%s). \n\nTo activate your banner ad campaign, please:\n\n1. Upload your banner ad using our upload form\n2. Choose your preferred ad zone\n3. Wait for approval (if required)\n\nOnce approved and your payment is processed, your banner ad will go live automatically.\n\nYou can manage your campaigns and view analytics in your dashboard.\n\nBest regards,\nThe Team"
msgstr ""

#: woocommerce/class-banner-ads-woocommerce.php:220
msgid "Your Banner Ad Campaign is Now Live!"
msgstr ""

#: woocommerce/class-banner-ads-woocommerce.php:222
msgid "Hi %s,\n\nGreat news! Your banner ad campaign \"%s\" is now live.\n\nCampaign Details:\n- Start Date: %s\n- End Date: %s\n- Order: #%s\n\nYou can track your campaign performance in your dashboard.\n\nBest regards,\nThe Team"
msgstr ""
