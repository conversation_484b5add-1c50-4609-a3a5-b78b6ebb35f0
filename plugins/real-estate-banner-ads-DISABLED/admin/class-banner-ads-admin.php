<?php
/**
 * The admin-specific functionality of the plugin
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/admin
 */

/**
 * The admin-specific functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for admin-specific functionality.
 */
class Banner_Ads_Admin {

	/**
	 * The ID of this plugin.
	 *
	 * @var string $plugin_name The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @var string $version The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @param string $plugin_name The name of this plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;

		// Include the Banner_Zone class
		require_once REAL_ESTATE_BANNER_ADS_PATH . 'models/class-banner-zone.php';
	}

	/**
	 * Register the stylesheets for the admin area.
	 */
	public function enqueue_styles() {
		wp_enqueue_style( 
			$this->plugin_name, 
			REAL_ESTATE_BANNER_ADS_URL . 'admin/css/banner-ads-admin.css', 
			array(), 
			$this->version, 
			'all' 
		);
	}

	/**
	 * Register the JavaScript for the admin area.
	 */
	public function enqueue_scripts() {
		// Enqueue WordPress media library for campaign edit pages
		global $pagenow, $post_type;
		if ( ( $pagenow === 'post.php' || $pagenow === 'post-new.php' ) && $post_type === 'banner_campaign' ) {
			wp_enqueue_media();
		}

		// Also enqueue media library for our admin pages
		$screen = get_current_screen();
		if ( $screen && strpos( $screen->id, 'banner-ads' ) !== false ) {
			wp_enqueue_media();
		}

		wp_enqueue_script(
			$this->plugin_name,
			REAL_ESTATE_BANNER_ADS_URL . 'admin/js/banner-ads-admin.js',
			array( 'jquery', 'wp-util' ),
			$this->version,
			true
		);

		// Localize script for AJAX
		wp_localize_script( $this->plugin_name, 'banner_ads_admin_ajax', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce( 'banner_ads_admin_nonce' ),
			'media_available' => function_exists( 'wp_enqueue_media' )
		));
	}

	/**
	 * Add admin menu items.
	 */
	public function add_admin_menu() {
		// Main menu page
		add_menu_page(
			__( 'Banner Ads', 'real-estate-banner-ads' ),
			__( 'Banner Ads', 'real-estate-banner-ads' ),
			'manage_options',
			'banner-ads',
			array( $this, 'display_admin_page' ),
			'dashicons-megaphone',
			30
		);

		// Campaigns submenu
		add_submenu_page(
			'banner-ads',
			__( 'All Campaigns', 'real-estate-banner-ads' ),
			__( 'All Campaigns', 'real-estate-banner-ads' ),
			'manage_options',
			'banner-ads',
			array( $this, 'display_admin_page' )
		);

		// Zones submenu
		add_submenu_page(
			'banner-ads',
			__( 'Ad Zones', 'real-estate-banner-ads' ),
			__( 'Ad Zones', 'real-estate-banner-ads' ),
			'manage_options',
			'banner-ads-zones',
			array( $this, 'display_zones_page' )
		);

		// Analytics submenu
		add_submenu_page(
			'banner-ads',
			__( 'Analytics', 'real-estate-banner-ads' ),
			__( 'Analytics', 'real-estate-banner-ads' ),
			'manage_options',
			'banner-ads-analytics',
			array( $this, 'display_analytics_page' )
		);

		// Settings submenu
		add_submenu_page(
			'banner-ads',
			__( 'Settings', 'real-estate-banner-ads' ),
			__( 'Settings', 'real-estate-banner-ads' ),
			'manage_options',
			'banner-ads-settings',
			array( $this, 'display_settings_page' )
		);
	}

	/**
	 * Register custom post types.
	 */
	public function register_post_types() {
		// Banner Campaign post type
		$labels = array(
			'name'                  => _x( 'Banner Campaigns', 'Post type general name', 'real-estate-banner-ads' ),
			'singular_name'         => _x( 'Banner Campaign', 'Post type singular name', 'real-estate-banner-ads' ),
			'menu_name'             => _x( 'Banner Campaigns', 'Admin Menu text', 'real-estate-banner-ads' ),
			'name_admin_bar'        => _x( 'Banner Campaign', 'Add New on Toolbar', 'real-estate-banner-ads' ),
			'add_new'               => __( 'Add New', 'real-estate-banner-ads' ),
			'add_new_item'          => __( 'Add New Banner Campaign', 'real-estate-banner-ads' ),
			'new_item'              => __( 'New Banner Campaign', 'real-estate-banner-ads' ),
			'edit_item'             => __( 'Edit Banner Campaign', 'real-estate-banner-ads' ),
			'view_item'             => __( 'View Banner Campaign', 'real-estate-banner-ads' ),
			'all_items'             => __( 'All Banner Campaigns', 'real-estate-banner-ads' ),
			'search_items'          => __( 'Search Banner Campaigns', 'real-estate-banner-ads' ),
			'parent_item_colon'     => __( 'Parent Banner Campaigns:', 'real-estate-banner-ads' ),
			'not_found'             => __( 'No banner campaigns found.', 'real-estate-banner-ads' ),
			'not_found_in_trash'    => __( 'No banner campaigns found in Trash.', 'real-estate-banner-ads' ),
		);

		$args = array(
			'labels'             => $labels,
			'public'             => false,
			'publicly_queryable' => false,
			'show_ui'            => true,
			'show_in_menu'       => 'banner-ads', // Add to our custom menu
			'query_var'          => true,
			'rewrite'            => array( 'slug' => 'banner-campaign' ),
			'capability_type'    => 'post',
			'has_archive'        => false,
			'hierarchical'       => false,
			'menu_position'      => null,
			'supports'           => array( 'title', 'editor', 'author', 'custom-fields' ),
			'show_in_rest'       => false,
		);

		register_post_type( 'banner_campaign', $args );

		// Add meta boxes for campaign management
		add_action( 'add_meta_boxes', array( $this, 'add_campaign_meta_boxes' ) );
		add_action( 'save_post', array( $this, 'save_campaign_meta_data' ) );

		// AJAX handler for getting a single zone
		add_action('wp_ajax_get_banner_zone', array($this, 'get_banner_zone'));
	}

	/**
	 * Register custom taxonomies.
	 */
	public function register_taxonomies() {
		// Banner Status taxonomy
		$labels = array(
			'name'              => _x( 'Campaign Status', 'taxonomy general name', 'real-estate-banner-ads' ),
			'singular_name'     => _x( 'Status', 'taxonomy singular name', 'real-estate-banner-ads' ),
			'search_items'      => __( 'Search Statuses', 'real-estate-banner-ads' ),
			'all_items'         => __( 'All Statuses', 'real-estate-banner-ads' ),
			'edit_item'         => __( 'Edit Status', 'real-estate-banner-ads' ),
			'update_item'       => __( 'Update Status', 'real-estate-banner-ads' ),
			'add_new_item'      => __( 'Add New Status', 'real-estate-banner-ads' ),
			'new_item_name'     => __( 'New Status Name', 'real-estate-banner-ads' ),
			'menu_name'         => __( 'Status', 'real-estate-banner-ads' ),
		);

		$args = array(
			'hierarchical'      => false,
			'labels'            => $labels,
			'show_ui'           => true,
			'show_admin_column' => true,
			'query_var'         => true,
			'rewrite'           => array( 'slug' => 'campaign-status' ),
			'show_in_rest'      => false,
		);

		register_taxonomy( 'campaign_status', array( 'banner_campaign' ), $args );

		// Add default terms
		if ( ! term_exists( 'pending', 'campaign_status' ) ) {
			wp_insert_term( 'Pending Approval', 'campaign_status', array( 'slug' => 'pending' ) );
		}
		if ( ! term_exists( 'approved', 'campaign_status' ) ) {
			wp_insert_term( 'Approved', 'campaign_status', array( 'slug' => 'approved' ) );
		}
		if ( ! term_exists( 'active', 'campaign_status' ) ) {
			wp_insert_term( 'Active', 'campaign_status', array( 'slug' => 'active' ) );
		}
		if ( ! term_exists( 'expired', 'campaign_status' ) ) {
			wp_insert_term( 'Expired', 'campaign_status', array( 'slug' => 'expired' ) );
		}
		if ( ! term_exists( 'rejected', 'campaign_status' ) ) {
			wp_insert_term( 'Rejected', 'campaign_status', array( 'slug' => 'rejected' ) );
		}
	}

	/**
	 * Display the main admin page.
	 */
	public function display_admin_page() {
		include_once REAL_ESTATE_BANNER_ADS_PATH . 'admin/partials/banner-ads-admin-display.php';
	}

	/**
	 * Display the zones management page.
	 */
	public function display_zones_page() {
		include_once REAL_ESTATE_BANNER_ADS_PATH . 'admin/partials/banner-ads-zones-display.php';
	}

	/**
	 * Display the analytics page.
	 */
	public function display_analytics_page() {
		include_once REAL_ESTATE_BANNER_ADS_PATH . 'admin/partials/banner-ads-analytics-display.php';
	}

	/**
	 * Display the settings page.
	 */
	public function display_settings_page() {
		include_once REAL_ESTATE_BANNER_ADS_PATH . 'admin/partials/banner-ads-settings-display.php';
	}

	/**
	 * Add meta boxes for campaign management.
	 */
	public function add_campaign_meta_boxes() {
		add_meta_box(
			'banner_campaign_details',
			__( 'Campaign Details', 'real-estate-banner-ads' ),
			array( $this, 'campaign_details_meta_box' ),
			'banner_campaign',
			'normal',
			'high'
		);

		add_meta_box(
			'banner_campaign_status',
			__( 'Campaign Status & Scheduling', 'real-estate-banner-ads' ),
			array( $this, 'campaign_status_meta_box' ),
			'banner_campaign',
			'side',
			'high'
		);

		add_meta_box(
			'banner_campaign_analytics',
			__( 'Campaign Analytics', 'real-estate-banner-ads' ),
			array( $this, 'campaign_analytics_meta_box' ),
			'banner_campaign',
			'side',
			'default'
		);
	}

	/**
	 * Campaign details meta box.
	 */
	public function campaign_details_meta_box( $post ) {
		wp_nonce_field( 'banner_campaign_meta_box', 'banner_campaign_meta_box_nonce' );

		$banner_type = get_post_meta( $post->ID, '_banner_type', true ) ?: 'image';
		$banner_zone = get_post_meta( $post->ID, '_banner_zone', true );
		$image_url = get_post_meta( $post->ID, '_banner_image_url', true );
		$html_content = get_post_meta( $post->ID, '_banner_html_content', true );
		$link_url = get_post_meta( $post->ID, '_banner_link_url', true );
		$link_target = get_post_meta( $post->ID, '_banner_link_target', true ) ?: '_blank';
		$alt_text = get_post_meta( $post->ID, '_banner_alt_text', true );

		$zones = Banner_Zone::get_options( false ); // Include inactive zones for admin
		?>
		<table class="form-table">
			<tr>
				<th><label for="banner_type"><?php esc_html_e( 'Banner Type', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<select name="banner_type" id="banner_type">
						<option value="image" <?php selected( $banner_type, 'image' ); ?>><?php esc_html_e( 'Image Banner', 'real-estate-banner-ads' ); ?></option>
						<option value="html" <?php selected( $banner_type, 'html' ); ?>><?php esc_html_e( 'HTML Banner', 'real-estate-banner-ads' ); ?></option>
					</select>
				</td>
			</tr>
			<tr>
				<th><label for="banner_zone"><?php esc_html_e( 'Ad Zone', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<select name="banner_zone" id="banner_zone">
						<option value=""><?php esc_html_e( 'Select Zone...', 'real-estate-banner-ads' ); ?></option>
						<?php foreach ( $zones as $zone_id => $zone_name ) : ?>
							<option value="<?php echo esc_attr( $zone_id ); ?>" <?php selected( $banner_zone, $zone_id ); ?>>
								<?php echo esc_html( $zone_name ); ?>
							</option>
						<?php endforeach; ?>
					</select>
				</td>
			</tr>
			<tr class="banner-image-fields" style="display: <?php echo $banner_type === 'image' ? 'table-row' : 'none'; ?>;">
				<th><label for="banner_image_url"><?php esc_html_e( 'Image URL', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<input type="url" name="banner_image_url" id="banner_image_url" value="<?php echo esc_attr( $image_url ); ?>" class="regular-text">
					<button type="button" class="button" id="upload_image_button"><?php esc_html_e( 'Upload Image', 'real-estate-banner-ads' ); ?></button>
					<div id="image_preview" style="margin-top: 10px;">
						<?php if ( $image_url ) : ?>
							<img src="<?php echo esc_url( $image_url ); ?>" style="max-width: 300px; max-height: 150px;">
						<?php endif; ?>
					</div>
				</td>
			</tr>
			<tr class="banner-image-fields" style="display: <?php echo $banner_type === 'image' ? 'table-row' : 'none'; ?>;">
				<th><label for="banner_alt_text"><?php esc_html_e( 'Alt Text', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<input type="text" name="banner_alt_text" id="banner_alt_text" value="<?php echo esc_attr( $alt_text ); ?>" class="regular-text">
				</td>
			</tr>
			<tr class="banner-html-fields" style="display: <?php echo $banner_type === 'html' ? 'table-row' : 'none'; ?>;">
				<th><label for="banner_html_content"><?php esc_html_e( 'HTML Content', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<textarea name="banner_html_content" id="banner_html_content" rows="10" class="large-text"><?php echo esc_textarea( $html_content ); ?></textarea>
					<p class="description"><?php esc_html_e( 'Enter HTML/CSS code. JavaScript is not allowed for security.', 'real-estate-banner-ads' ); ?></p>
				</td>
			</tr>
			<tr>
				<th><label for="banner_link_url"><?php esc_html_e( 'Link URL', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<input type="url" name="banner_link_url" id="banner_link_url" value="<?php echo esc_attr( $link_url ); ?>" class="regular-text">
				</td>
			</tr>
			<tr>
				<th><label for="banner_link_target"><?php esc_html_e( 'Link Target', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<select name="banner_link_target" id="banner_link_target">
						<option value="_blank" <?php selected( $link_target, '_blank' ); ?>><?php esc_html_e( 'New Window', 'real-estate-banner-ads' ); ?></option>
						<option value="_self" <?php selected( $link_target, '_self' ); ?>><?php esc_html_e( 'Same Window', 'real-estate-banner-ads' ); ?></option>
					</select>
				</td>
			</tr>
		</table>

		<script>
		jQuery(document).ready(function($) {
			// Toggle banner type fields
			$(document).off('change', '#banner_type').on('change', '#banner_type', function() {
				var type = $(this).val();
				$('.banner-image-fields, .banner-html-fields').hide();
				$('.banner-' + type + '-fields').show();
			});
		});
		</script>
		<?php
	}

	/**
	 * Campaign status meta box.
	 */
	public function campaign_status_meta_box( $post ) {
		$campaign_status = get_post_meta( $post->ID, '_campaign_status', true ) ?: 'pending';
		$start_date = get_post_meta( $post->ID, '_campaign_start_date', true );
		$end_date = get_post_meta( $post->ID, '_campaign_end_date', true );
		$order_id = get_post_meta( $post->ID, '_woocommerce_order_id', true );
		?>
		<table class="form-table">
			<tr>
				<th><label for="campaign_status"><?php esc_html_e( 'Status', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<select name="campaign_status" id="campaign_status">
						<option value="pending" <?php selected( $campaign_status, 'pending' ); ?>><?php esc_html_e( 'Pending', 'real-estate-banner-ads' ); ?></option>
						<option value="approved" <?php selected( $campaign_status, 'approved' ); ?>><?php esc_html_e( 'Approved', 'real-estate-banner-ads' ); ?></option>
						<option value="active" <?php selected( $campaign_status, 'active' ); ?>><?php esc_html_e( 'Active', 'real-estate-banner-ads' ); ?></option>
						<option value="paused" <?php selected( $campaign_status, 'paused' ); ?>><?php esc_html_e( 'Paused', 'real-estate-banner-ads' ); ?></option>
						<option value="expired" <?php selected( $campaign_status, 'expired' ); ?>><?php esc_html_e( 'Expired', 'real-estate-banner-ads' ); ?></option>
						<option value="rejected" <?php selected( $campaign_status, 'rejected' ); ?>><?php esc_html_e( 'Rejected', 'real-estate-banner-ads' ); ?></option>
					</select>
					<p class="description"><?php esc_html_e( 'Admin can set any status directly', 'real-estate-banner-ads' ); ?></p>
				</td>
			</tr>
			<tr>
				<th><label for="campaign_start_date"><?php esc_html_e( 'Start Date', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<input type="datetime-local" name="campaign_start_date" id="campaign_start_date"
						   value="<?php echo $start_date ? esc_attr( date( 'Y-m-d\TH:i', strtotime( $start_date ) ) ) : ''; ?>">
				</td>
			</tr>
			<tr>
				<th><label for="campaign_end_date"><?php esc_html_e( 'End Date', 'real-estate-banner-ads' ); ?></label></th>
				<td>
					<input type="datetime-local" name="campaign_end_date" id="campaign_end_date"
						   value="<?php echo $end_date ? esc_attr( date( 'Y-m-d\TH:i', strtotime( $end_date ) ) ) : ''; ?>">
				</td>
			</tr>
			<?php if ( $order_id ) : ?>
			<tr>
				<th><?php esc_html_e( 'WooCommerce Order', 'real-estate-banner-ads' ); ?></th>
				<td>
					<a href="<?php echo esc_url( admin_url( 'post.php?post=' . $order_id . '&action=edit' ) ); ?>" target="_blank">
						<?php printf( esc_html__( 'Order #%s', 'real-estate-banner-ads' ), $order_id ); ?>
					</a>
				</td>
			</tr>
			<?php endif; ?>
		</table>

		<div class="admin-quick-actions" style="margin-top: 15px;">
			<h4><?php esc_html_e( 'Quick Actions', 'real-estate-banner-ads' ); ?></h4>
			<button type="button" class="button" id="activate_now"><?php esc_html_e( 'Activate Now', 'real-estate-banner-ads' ); ?></button>
			<button type="button" class="button" id="set_7_days"><?php esc_html_e( 'Set 7 Days', 'real-estate-banner-ads' ); ?></button>
			<button type="button" class="button" id="set_30_days"><?php esc_html_e( 'Set 30 Days', 'real-estate-banner-ads' ); ?></button>
		</div>

		<script>
		jQuery(document).ready(function($) {
			$('#activate_now').on('click', function() {
				$('#campaign_status').val('active');
				var now = new Date();
				var start = now.toISOString().slice(0, 16);
				$('#campaign_start_date').val(start);
			});

			$('#set_7_days').on('click', function() {
				var now = new Date();
				var start = now.toISOString().slice(0, 16);
				var end = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16);
				$('#campaign_start_date').val(start);
				$('#campaign_end_date').val(end);
				$('#campaign_status').val('active');
			});

			$('#set_30_days').on('click', function() {
				var now = new Date();
				var start = now.toISOString().slice(0, 16);
				var end = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16);
				$('#campaign_start_date').val(start);
				$('#campaign_end_date').val(end);
				$('#campaign_status').val('active');
			});
		});
		</script>
		<?php
	}

	/**
	 * Campaign analytics meta box.
	 */
	public function campaign_analytics_meta_box( $post ) {
		if ( $post->ID ) {
			$analytics = Banner_Analytics::get_daily_analytics( $post->ID, date( 'Y-m-d', strtotime( '-30 days' ) ), date( 'Y-m-d' ) );
			$total_impressions = Banner_Analytics::get_impressions( $post->ID );
			$total_clicks = Banner_Analytics::get_clicks( $post->ID );
			$ctr = $total_impressions > 0 ? round( ( $total_clicks / $total_impressions ) * 100, 2 ) : 0;
			?>
			<div class="analytics-summary">
				<p><strong><?php esc_html_e( 'Total Impressions:', 'real-estate-banner-ads' ); ?></strong> <?php echo esc_html( number_format( $total_impressions ) ); ?></p>
				<p><strong><?php esc_html_e( 'Total Clicks:', 'real-estate-banner-ads' ); ?></strong> <?php echo esc_html( number_format( $total_clicks ) ); ?></p>
				<p><strong><?php esc_html_e( 'CTR:', 'real-estate-banner-ads' ); ?></strong> <?php echo esc_html( $ctr ); ?>%</p>
			</div>
			<p><a href="<?php echo esc_url( admin_url( 'admin.php?page=banner-ads-analytics&campaign_filter=' . $post->ID ) ); ?>" class="button">
				<?php esc_html_e( 'View Detailed Analytics', 'real-estate-banner-ads' ); ?>
			</a></p>
			<?php
		} else {
			echo '<p>' . esc_html__( 'Analytics will be available after saving the campaign.', 'real-estate-banner-ads' ) . '</p>';
		}
	}

	/**
	 * Save campaign meta data.
	 */
	public function save_campaign_meta_data( $post_id ) {
		if ( ! isset( $_POST['banner_campaign_meta_box_nonce'] ) ) {
			return;
		}

		if ( ! wp_verify_nonce( $_POST['banner_campaign_meta_box_nonce'], 'banner_campaign_meta_box' ) ) {
			return;
		}

		if ( defined( 'DOING_AUTOSAVE' ) && DOING_AUTOSAVE ) {
			return;
		}

		if ( ! current_user_can( 'edit_post', $post_id ) ) {
			return;
		}

		// Save meta fields
		$meta_fields = array(
			'_banner_type' => 'banner_type',
			'_banner_zone' => 'banner_zone',
			'_banner_image_url' => 'banner_image_url',
			'_banner_alt_text' => 'banner_alt_text',
			'_banner_html_content' => 'banner_html_content',
			'_banner_link_url' => 'banner_link_url',
			'_banner_link_target' => 'banner_link_target',
			'_campaign_status' => 'campaign_status',
			'_campaign_start_date' => 'campaign_start_date',
			'_campaign_end_date' => 'campaign_end_date'
		);

		foreach ( $meta_fields as $meta_key => $form_field ) {
			if ( isset( $_POST[ $form_field ] ) ) {
				$value = $_POST[ $form_field ];

				if ( $form_field === 'banner_html_content' ) {
					$value = wp_kses_post( $value );
				} elseif ( in_array( $form_field, array( 'banner_image_url', 'banner_link_url' ) ) ) {
					$value = esc_url_raw( $value );
				} elseif ( in_array( $form_field, array( 'campaign_start_date', 'campaign_end_date' ) ) ) {
					$value = $value ? date( 'Y-m-d H:i:s', strtotime( $value ) ) : '';
				} else {
					$value = sanitize_text_field( $value );
				}

				update_post_meta( $post_id, $meta_key, $value );
			}
		}

		// Update taxonomy term
		if ( isset( $_POST['campaign_status'] ) ) {
			wp_set_post_terms( $post_id, array( sanitize_text_field( $_POST['campaign_status'] ) ), 'campaign_status' );
		}
	}

	/**
	 * AJAX handler for getting a single banner zone.
	 */
	public function get_banner_zone() {
		check_ajax_referer('banner_ads_admin_nonce', 'nonce');

		if (!isset($_POST['zone_id'])) {
			wp_send_json_error('Zone ID is missing.');
		}

		$zone_id = sanitize_text_field($_POST['zone_id']);
		$zone = Banner_Zone::get_by_id($zone_id);

		if (is_wp_error($zone)) {
			wp_send_json_error($zone->get_error_message());
		} else {
			wp_send_json_success(get_object_vars($zone));
		}
	}
}
