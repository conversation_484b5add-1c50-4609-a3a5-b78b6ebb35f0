<?php
/**
 * Provide an admin area view for analytics
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// Get date range from request
$date_range = $_GET['date_range'] ?? '30days';
$campaign_filter = $_GET['campaign_filter'] ?? '';

// Calculate date range
$end_date = date( 'Y-m-d' );
switch ( $date_range ) {
	case '7days':
		$start_date = date( 'Y-m-d', strtotime( '-7 days' ) );
		break;
	case '30days':
		$start_date = date( 'Y-m-d', strtotime( '-30 days' ) );
		break;
	case '90days':
		$start_date = date( 'Y-m-d', strtotime( '-90 days' ) );
		break;
	case 'custom':
		$start_date = $_GET['start_date'] ?? date( 'Y-m-d', strtotime( '-30 days' ) );
		$end_date = $_GET['end_date'] ?? date( 'Y-m-d' );
		break;
	default:
		$start_date = date( 'Y-m-d', strtotime( '-30 days' ) );
}

// Get all campaigns for filter
$all_campaigns = get_posts( array(
	'post_type' => 'banner_campaign',
	'post_status' => 'publish',
	'posts_per_page' => -1,
	'orderby' => 'title',
	'order' => 'ASC'
));

// Get analytics data
if ( $campaign_filter ) {
	$campaign_ids = array( intval( $campaign_filter ) );
} else {
	$campaign_ids = wp_list_pluck( $all_campaigns, 'ID' );
}

$analytics_summary = Banner_Analytics::get_campaigns_summary( $campaign_ids, $start_date, $end_date );

// Get daily analytics for chart
$daily_analytics = array();
if ( ! empty( $campaign_ids ) ) {
	$daily_analytics = Banner_Analytics::get_daily_analytics( $campaign_ids[0], $start_date, $end_date );
}
?>

<div class="wrap analytics-page">
	<h1 class="wp-heading-inline"><?php esc_html_e( 'Banner Ads Analytics', 'real-estate-banner-ads' ); ?></h1>
	<hr class="wp-header-end">

	<!-- Analytics Filters -->
	<div class="analytics-container">
		<div class="analytics-header">
			<h2><?php esc_html_e( 'Performance Overview', 'real-estate-banner-ads' ); ?></h2>
			<div class="analytics-filters">
				<form method="get" id="analytics-filter-form">
					<input type="hidden" name="page" value="banner-ads-analytics">
					
					<select name="date_range" id="analytics-date-range">
						<option value="7days" <?php selected( $date_range, '7days' ); ?>><?php esc_html_e( 'Last 7 Days', 'real-estate-banner-ads' ); ?></option>
						<option value="30days" <?php selected( $date_range, '30days' ); ?>><?php esc_html_e( 'Last 30 Days', 'real-estate-banner-ads' ); ?></option>
						<option value="90days" <?php selected( $date_range, '90days' ); ?>><?php esc_html_e( 'Last 90 Days', 'real-estate-banner-ads' ); ?></option>
						<option value="custom" <?php selected( $date_range, 'custom' ); ?>><?php esc_html_e( 'Custom Range', 'real-estate-banner-ads' ); ?></option>
					</select>

					<div id="custom-date-range" style="display: <?php echo $date_range === 'custom' ? 'inline-block' : 'none'; ?>;">
						<input type="date" name="start_date" value="<?php echo esc_attr( $start_date ); ?>">
						<input type="date" name="end_date" value="<?php echo esc_attr( $end_date ); ?>">
					</div>

					<select name="campaign_filter">
						<option value=""><?php esc_html_e( 'All Campaigns', 'real-estate-banner-ads' ); ?></option>
						<?php foreach ( $all_campaigns as $campaign ) : ?>
							<option value="<?php echo esc_attr( $campaign->ID ); ?>" <?php selected( $campaign_filter, $campaign->ID ); ?>>
								<?php echo esc_html( $campaign->post_title ); ?>
							</option>
						<?php endforeach; ?>
					</select>

					<input type="submit" class="button" value="<?php esc_attr_e( 'Apply Filters', 'real-estate-banner-ads' ); ?>">
				</form>
			</div>
		</div>

		<!-- Summary Stats -->
		<div class="analytics-summary">
			<div class="summary-item">
				<span class="summary-number"><?php echo esc_html( number_format( $analytics_summary['total_impressions'] ) ); ?></span>
				<span class="summary-label"><?php esc_html_e( 'Total Impressions', 'real-estate-banner-ads' ); ?></span>
			</div>
			<div class="summary-item">
				<span class="summary-number"><?php echo esc_html( number_format( $analytics_summary['total_clicks'] ) ); ?></span>
				<span class="summary-label"><?php esc_html_e( 'Total Clicks', 'real-estate-banner-ads' ); ?></span>
			</div>
			<div class="summary-item">
				<span class="summary-number"><?php echo esc_html( $analytics_summary['average_ctr'] ); ?>%</span>
				<span class="summary-label"><?php esc_html_e( 'Average CTR', 'real-estate-banner-ads' ); ?></span>
			</div>
			<div class="summary-item">
				<span class="summary-number"><?php echo esc_html( count( $analytics_summary['campaigns'] ) ); ?></span>
				<span class="summary-label"><?php esc_html_e( 'Active Campaigns', 'real-estate-banner-ads' ); ?></span>
			</div>
		</div>

		<!-- Chart Placeholder -->
		<div class="chart-container">
			<canvas id="analytics-chart" width="400" height="200"></canvas>
		</div>
	</div>

	<!-- Campaign Performance Table -->
	<div class="analytics-container">
		<h2><?php esc_html_e( 'Campaign Performance', 'real-estate-banner-ads' ); ?></h2>
		
		<table class="wp-list-table widefat fixed striped">
			<thead>
				<tr>
					<th><?php esc_html_e( 'Campaign', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'Owner', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'Zone', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'Status', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'Impressions', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'Clicks', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'CTR', 'real-estate-banner-ads' ); ?></th>
				</tr>
			</thead>
			<tbody>
				<?php if ( ! empty( $analytics_summary['campaigns'] ) ) : ?>
					<?php foreach ( $analytics_summary['campaigns'] as $campaign_data ) : ?>
						<?php
						$campaign_post = get_post( $campaign_data['campaign_id'] );
						$campaign_status = get_post_meta( $campaign_post->ID, '_campaign_status', true );
						$campaign_zone = get_post_meta( $campaign_post->ID, '_banner_zone', true );
						$author = get_user_by( 'id', $campaign_post->post_author );
						
						$zones = get_option( 'banner_ads_zones', array() );
						$zone_name = $zones[ $campaign_zone ]['name'] ?? $campaign_zone;
						?>
						<tr>
							<td>
								<strong><?php echo esc_html( $campaign_post->post_title ); ?></strong>
							</td>
							<td>
								<?php echo $author ? esc_html( $author->display_name ) : __( 'Unknown', 'real-estate-banner-ads' ); ?>
							</td>
							<td>
								<?php echo esc_html( $zone_name ); ?>
							</td>
							<td>
								<span class="banner-status banner-status-<?php echo esc_attr( $campaign_status ); ?>">
									<?php echo esc_html( ucfirst( $campaign_status ) ); ?>
								</span>
							</td>
							<td>
								<?php echo esc_html( number_format( $campaign_data['impressions'] ) ); ?>
							</td>
							<td>
								<?php echo esc_html( number_format( $campaign_data['clicks'] ) ); ?>
							</td>
							<td>
								<?php echo esc_html( $campaign_data['ctr'] ); ?>%
							</td>
						</tr>
					<?php endforeach; ?>
				<?php else : ?>
					<tr>
						<td colspan="7" class="no-items">
							<?php esc_html_e( 'No campaign data found for the selected period.', 'real-estate-banner-ads' ); ?>
						</td>
					</tr>
				<?php endif; ?>
			</tbody>
		</table>
	</div>

	<!-- Top Performing Zones -->
	<?php
	$zones = Banner_Zone::get_all( true );
	$zone_performance = array();
	
	foreach ( $zones as $zone ) {
		$zone_campaigns = get_posts( array(
			'post_type' => 'banner_campaign',
			'post_status' => 'publish',
			'posts_per_page' => -1,
			'meta_query' => array(
				array(
					'key' => '_banner_zone',
					'value' => $zone->id,
					'compare' => '='
				)
			),
			'fields' => 'ids'
		));
		
		if ( ! empty( $zone_campaigns ) ) {
			$zone_summary = Banner_Analytics::get_campaigns_summary( $zone_campaigns, $start_date, $end_date );
			$zone_performance[] = array(
				'zone' => $zone,
				'impressions' => $zone_summary['total_impressions'],
				'clicks' => $zone_summary['total_clicks'],
				'ctr' => $zone_summary['average_ctr']
			);
		}
	}
	
	// Sort by impressions
	usort( $zone_performance, function( $a, $b ) {
		return $b['impressions'] - $a['impressions'];
	});
	?>

	<div class="analytics-container">
		<h2><?php esc_html_e( 'Zone Performance', 'real-estate-banner-ads' ); ?></h2>
		
		<table class="wp-list-table widefat fixed striped">
			<thead>
				<tr>
					<th><?php esc_html_e( 'Zone', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'Active Campaigns', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'Capacity', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'Impressions', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'Clicks', 'real-estate-banner-ads' ); ?></th>
					<th><?php esc_html_e( 'CTR', 'real-estate-banner-ads' ); ?></th>
				</tr>
			</thead>
			<tbody>
				<?php if ( ! empty( $zone_performance ) ) : ?>
					<?php foreach ( $zone_performance as $zone_data ) : ?>
						<?php
						$zone = $zone_data['zone'];
						$active_campaigns = $zone->get_active_campaigns();
						?>
						<tr>
							<td>
								<strong><?php echo esc_html( $zone->name ); ?></strong><br>
								<small><?php echo esc_html( $zone->id ); ?></small>
							</td>
							<td>
								<?php echo esc_html( count( $active_campaigns ) ); ?>
							</td>
							<td>
								<?php echo esc_html( count( $active_campaigns ) ); ?> / <?php echo esc_html( $zone->max_banners ); ?>
								<div class="capacity-bar">
									<div class="capacity-fill" style="width: <?php echo esc_attr( ( count( $active_campaigns ) / $zone->max_banners ) * 100 ); ?>%;"></div>
								</div>
							</td>
							<td>
								<?php echo esc_html( number_format( $zone_data['impressions'] ) ); ?>
							</td>
							<td>
								<?php echo esc_html( number_format( $zone_data['clicks'] ) ); ?>
							</td>
							<td>
								<?php echo esc_html( $zone_data['ctr'] ); ?>%
							</td>
						</tr>
					<?php endforeach; ?>
				<?php else : ?>
					<tr>
						<td colspan="6" class="no-items">
							<?php esc_html_e( 'No zone performance data available.', 'real-estate-banner-ads' ); ?>
						</td>
					</tr>
				<?php endif; ?>
			</tbody>
		</table>
	</div>
</div>

<style>
.capacity-bar {
	width: 100px;
	height: 8px;
	background: #e0e0e0;
	border-radius: 4px;
	overflow: hidden;
	margin-top: 4px;
}

.capacity-fill {
	height: 100%;
	background: #0073aa;
	transition: width 0.3s ease;
}

.no-items {
	text-align: center;
	padding: 20px;
	color: #666;
}
</style>

<script>
jQuery(document).ready(function($) {
	// Show/hide custom date range
	$('#analytics-date-range').on('change', function() {
		if ($(this).val() === 'custom') {
			$('#custom-date-range').show();
		} else {
			$('#custom-date-range').hide();
		}
	});

	// Auto-submit form on filter change
	$('#analytics-date-range, select[name="campaign_filter"]').on('change', function() {
		if ($(this).attr('name') !== 'date_range' || $(this).val() !== 'custom') {
			$('#analytics-filter-form').submit();
		}
	});

	// Simple chart implementation (you can replace with Chart.js or similar)
	var canvas = document.getElementById('analytics-chart');
	if (canvas) {
		var ctx = canvas.getContext('2d');
		ctx.fillStyle = '#f0f0f0';
		ctx.fillRect(0, 0, canvas.width, canvas.height);
		ctx.fillStyle = '#333';
		ctx.font = '16px Arial';
		ctx.textAlign = 'center';
		ctx.fillText('Analytics Chart Placeholder', canvas.width / 2, canvas.height / 2);
		ctx.font = '12px Arial';
		ctx.fillText('Integrate Chart.js or similar library for detailed charts', canvas.width / 2, canvas.height / 2 + 20);
	}
});
</script>
