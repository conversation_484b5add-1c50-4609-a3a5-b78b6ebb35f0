<?php
/**
 * Provide an admin area view for the plugin
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// Get campaigns data
$campaigns_query = new WP_Query( array(
	'post_type' => 'banner_campaign',
	'post_status' => 'publish',
	'posts_per_page' => 20,
	'orderby' => 'date',
	'order' => 'DESC'
));

// Get status filter
$status_filter = $_GET['status'] ?? '';

if ( $status_filter ) {
	$campaigns_query = new WP_Query( array(
		'post_type' => 'banner_campaign',
		'post_status' => 'publish',
		'posts_per_page' => 20,
		'meta_query' => array(
			array(
				'key' => '_campaign_status',
				'value' => $status_filter,
				'compare' => '='
			)
		),
		'orderby' => 'date',
		'order' => 'DESC'
	));
}

// Get summary stats
$total_campaigns = wp_count_posts( 'banner_campaign' )->publish;
$active_campaigns = count( Banner_Campaign::get_by_status( 'active' ) );
$pending_campaigns = count( Banner_Campaign::get_by_status( 'pending' ) );
$expired_campaigns = count( Banner_Campaign::get_by_status( 'expired' ) );
?>

<div class="wrap">
	<h1 class="wp-heading-inline"><?php esc_html_e( 'Banner Ads Manager', 'real-estate-banner-ads' ); ?></h1>
	<a href="<?php echo esc_url( admin_url( 'post-new.php?post_type=banner_campaign' ) ); ?>" class="page-title-action"><?php esc_html_e( 'Create Campaign', 'real-estate-banner-ads' ); ?></a>
	<a href="<?php echo esc_url( admin_url( 'admin.php?page=banner-ads-zones' ) ); ?>" class="page-title-action"><?php esc_html_e( 'Manage Zones', 'real-estate-banner-ads' ); ?></a>
	<hr class="wp-header-end">

	<!-- Summary Stats -->
	<div class="banner-ads-stats">
		<div class="banner-ads-stat-cards">
			<div class="banner-ads-stat-card">
				<h3><?php echo esc_html( $total_campaigns ); ?></h3>
				<p><?php esc_html_e( 'Total Campaigns', 'real-estate-banner-ads' ); ?></p>
			</div>
			<div class="banner-ads-stat-card active">
				<h3><?php echo esc_html( $active_campaigns ); ?></h3>
				<p><?php esc_html_e( 'Active Campaigns', 'real-estate-banner-ads' ); ?></p>
			</div>
			<div class="banner-ads-stat-card pending">
				<h3><?php echo esc_html( $pending_campaigns ); ?></h3>
				<p><?php esc_html_e( 'Pending Approval', 'real-estate-banner-ads' ); ?></p>
			</div>
			<div class="banner-ads-stat-card expired">
				<h3><?php echo esc_html( $expired_campaigns ); ?></h3>
				<p><?php esc_html_e( 'Expired Campaigns', 'real-estate-banner-ads' ); ?></p>
			</div>
		</div>
	</div>

	<!-- Filters -->
	<div class="tablenav top">
		<div class="alignleft actions">
			<select name="status" id="status-filter">
				<option value=""><?php esc_html_e( 'All Statuses', 'real-estate-banner-ads' ); ?></option>
				<option value="pending" <?php selected( $status_filter, 'pending' ); ?>><?php esc_html_e( 'Pending', 'real-estate-banner-ads' ); ?></option>
				<option value="approved" <?php selected( $status_filter, 'approved' ); ?>><?php esc_html_e( 'Approved', 'real-estate-banner-ads' ); ?></option>
				<option value="active" <?php selected( $status_filter, 'active' ); ?>><?php esc_html_e( 'Active', 'real-estate-banner-ads' ); ?></option>
				<option value="expired" <?php selected( $status_filter, 'expired' ); ?>><?php esc_html_e( 'Expired', 'real-estate-banner-ads' ); ?></option>
				<option value="rejected" <?php selected( $status_filter, 'rejected' ); ?>><?php esc_html_e( 'Rejected', 'real-estate-banner-ads' ); ?></option>
			</select>
			<input type="submit" id="doaction" class="button action" value="<?php esc_attr_e( 'Filter', 'real-estate-banner-ads' ); ?>">
		</div>
	</div>

	<!-- Campaigns Table -->
	<table class="wp-list-table widefat fixed striped posts">
		<thead>
			<tr>
				<th scope="col" class="manage-column column-cb check-column">
					<input type="checkbox" id="cb-select-all-1">
				</th>
				<th scope="col" class="manage-column column-title column-primary">
					<?php esc_html_e( 'Campaign', 'real-estate-banner-ads' ); ?>
				</th>
				<th scope="col" class="manage-column">
					<?php esc_html_e( 'Owner', 'real-estate-banner-ads' ); ?>
				</th>
				<th scope="col" class="manage-column">
					<?php esc_html_e( 'Zone', 'real-estate-banner-ads' ); ?>
				</th>
				<th scope="col" class="manage-column">
					<?php esc_html_e( 'Status', 'real-estate-banner-ads' ); ?>
				</th>
				<th scope="col" class="manage-column">
					<?php esc_html_e( 'Dates', 'real-estate-banner-ads' ); ?>
				</th>
				<th scope="col" class="manage-column">
					<?php esc_html_e( 'Performance', 'real-estate-banner-ads' ); ?>
				</th>
			</tr>
		</thead>
		<tbody>
			<?php if ( $campaigns_query->have_posts() ) : ?>
				<?php while ( $campaigns_query->have_posts() ) : $campaigns_query->the_post(); ?>
					<?php
					$campaign_id = get_the_ID();
					$campaign = new Banner_Campaign( $campaign_id );
					$analytics = $campaign->get_analytics();
					$author = get_user_by( 'id', $campaign->user_id );
					$zones = get_option( 'banner_ads_zones', array() );
					$zone_name = $zones[ $campaign->zone ]['name'] ?? $campaign->zone;
					?>
					<tr>
						<th scope="row" class="check-column">
							<input type="checkbox" name="post[]" value="<?php echo esc_attr( $campaign_id ); ?>">
						</th>
						<td class="title column-title column-primary">
							<strong>
								<a href="<?php echo esc_url( get_edit_post_link( $campaign_id ) ); ?>">
									<?php echo esc_html( get_the_title() ); ?>
								</a>
							</strong>
							<div class="row-actions">
								<span class="edit">
									<a href="<?php echo esc_url( get_edit_post_link( $campaign_id ) ); ?>"><?php esc_html_e( 'Edit', 'real-estate-banner-ads' ); ?></a> |
								</span>
								<span class="view">
									<a href="<?php echo esc_url( get_permalink( $campaign_id ) ); ?>"><?php esc_html_e( 'View', 'real-estate-banner-ads' ); ?></a> |
								</span>
								<span class="trash">
									<a href="<?php echo esc_url( get_delete_post_link( $campaign_id ) ); ?>" class="submitdelete"><?php esc_html_e( 'Delete', 'real-estate-banner-ads' ); ?></a>
								</span>
							</div>
						</td>
						<td>
							<?php echo $author ? esc_html( $author->display_name ) : __( 'Unknown', 'real-estate-banner-ads' ); ?>
						</td>
						<td>
							<?php echo esc_html( $zone_name ); ?>
						</td>
						<td>
							<span class="banner-status banner-status-<?php echo esc_attr( $campaign->status ); ?>">
								<?php echo esc_html( ucfirst( $campaign->status ) ); ?>
							</span>
						</td>
						<td>
							<?php if ( $campaign->start_date ) : ?>
								<strong><?php esc_html_e( 'Start:', 'real-estate-banner-ads' ); ?></strong> <?php echo esc_html( date( 'M j, Y', strtotime( $campaign->start_date ) ) ); ?><br>
							<?php endif; ?>
							<?php if ( $campaign->end_date ) : ?>
								<strong><?php esc_html_e( 'End:', 'real-estate-banner-ads' ); ?></strong> <?php echo esc_html( date( 'M j, Y', strtotime( $campaign->end_date ) ) ); ?>
							<?php endif; ?>
						</td>
						<td>
							<div class="banner-performance">
								<div><strong><?php echo esc_html( number_format( $analytics['impressions'] ) ); ?></strong> <?php esc_html_e( 'views', 'real-estate-banner-ads' ); ?></div>
								<div><strong><?php echo esc_html( number_format( $analytics['clicks'] ) ); ?></strong> <?php esc_html_e( 'clicks', 'real-estate-banner-ads' ); ?></div>
								<div><strong><?php echo esc_html( $analytics['ctr'] ); ?>%</strong> <?php esc_html_e( 'CTR', 'real-estate-banner-ads' ); ?></div>
							</div>
						</td>
					</tr>
				<?php endwhile; ?>
			<?php else : ?>
				<tr>
					<td colspan="7" class="no-items">
						<?php esc_html_e( 'No banner campaigns found.', 'real-estate-banner-ads' ); ?>
					</td>
				</tr>
			<?php endif; ?>
		</tbody>
	</table>

	<?php wp_reset_postdata(); ?>
</div>

<script>
jQuery(document).ready(function($) {
	$('#status-filter').on('change', function() {
		var status = $(this).val();
		var url = new URL(window.location);
		if (status) {
			url.searchParams.set('status', status);
		} else {
			url.searchParams.delete('status');
		}
		window.location = url;
	});
});
</script>
