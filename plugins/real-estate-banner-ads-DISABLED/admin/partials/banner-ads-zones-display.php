<?php
/**
 * Provide an admin area view for managing ad zones
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// Handle form submissions
if ( isset( $_POST['action'] ) && check_admin_referer( 'zones_action', 'zones_nonce' ) ) {
	$action = sanitize_text_field( $_POST['action'] );
	
	if ( $action === 'add_zone' ) {
		$zone_data = array(
			'id' => sanitize_text_field( $_POST['zone_id'] ),
			'name' => sanitize_text_field( $_POST['zone_name'] ),
			'description' => sanitize_textarea_field( $_POST['zone_description'] ),
			'max_banners' => intval( $_POST['max_banners'] ),
			'dimensions' => sanitize_text_field( $_POST['dimensions'] ),
			'active' => ! empty( $_POST['active'] )
		);
		
		$result = Banner_Zone::create( $zone_data );
		
		if ( is_wp_error( $result ) ) {
			$error_message = $result->get_error_message();
		} else {
			$success_message = __( 'Zone created successfully!', 'real-estate-banner-ads' );
		}
	} elseif ( $action === 'update_zone' ) {
		$zone_id = sanitize_text_field( $_POST['zone_id'] );
		$zone_data = array(
			'name' => sanitize_text_field( $_POST['zone_name'] ),
			'description' => sanitize_textarea_field( $_POST['zone_description'] ),
			'max_banners' => intval( $_POST['max_banners'] ),
			'dimensions' => sanitize_text_field( $_POST['dimensions'] ),
			'active' => ! empty( $_POST['active'] )
		);
		
		$result = Banner_Zone::update( $zone_id, $zone_data );
		
		if ( is_wp_error( $result ) ) {
			$error_message = $result->get_error_message();
		} else {
			$success_message = __( 'Zone updated successfully!', 'real-estate-banner-ads' );
		}
	} elseif ( $action === 'delete_zone' ) {
		$zone_id = sanitize_text_field( $_POST['zone_id'] );
		$zone = new Banner_Zone( $zone_id );
		
		if ( $zone->delete() ) {
			$success_message = __( 'Zone deleted successfully!', 'real-estate-banner-ads' );
		} else {
			$error_message = __( 'Failed to delete zone.', 'real-estate-banner-ads' );
		}
	}
}

// Get all zones
$zones = Banner_Zone::get_all( false ); // Include inactive zones
?>

<div class="wrap banner-zones-page">
	<h1 class="wp-heading-inline"><?php esc_html_e( 'Ad Zones Management', 'real-estate-banner-ads' ); ?></h1>
	<a href="#" class="page-title-action" data-modal="add-zone-modal"><?php esc_html_e( 'Add New Zone', 'real-estate-banner-ads' ); ?></a>
	<hr class="wp-header-end">

	<?php if ( isset( $success_message ) ) : ?>
		<div class="notice notice-success is-dismissible">
			<p><?php echo esc_html( $success_message ); ?></p>
		</div>
	<?php endif; ?>

	<?php if ( isset( $error_message ) ) : ?>
		<div class="notice notice-error is-dismissible">
			<p><?php echo esc_html( $error_message ); ?></p>
		</div>
	<?php endif; ?>

	<!-- Zones Grid -->
	<div class="banner-zones-grid">
		<?php if ( ! empty( $zones ) ) : ?>
			<?php foreach ( $zones as $zone ) : ?>
				<?php
				$active_campaigns = $zone->get_active_campaigns();
				$available_slots = $zone->get_available_slots();
				?>
				<div class="banner-zone-card">
					<h4><?php echo esc_html( $zone->name ); ?></h4>
					<div class="zone-description">
						<?php echo esc_html( $zone->description ); ?>
					</div>
					
					<div class="zone-meta">
						<div class="zone-info">
							<strong><?php esc_html_e( 'ID:', 'real-estate-banner-ads' ); ?></strong> <?php echo esc_html( $zone->id ); ?><br>
							<strong><?php esc_html_e( 'Dimensions:', 'real-estate-banner-ads' ); ?></strong> <?php echo esc_html( $zone->dimensions ); ?><br>
							<strong><?php esc_html_e( 'Status:', 'real-estate-banner-ads' ); ?></strong> 
							<span class="status-badge <?php echo $zone->active ? 'active' : 'inactive'; ?>">
								<?php echo $zone->active ? esc_html__( 'Active', 'real-estate-banner-ads' ) : esc_html__( 'Inactive', 'real-estate-banner-ads' ); ?>
							</span>
						</div>
						
						<div class="zone-capacity <?php echo $available_slots === 0 ? 'full' : ''; ?>">
							<strong><?php echo esc_html( count( $active_campaigns ) ); ?></strong> / 
							<strong><?php echo esc_html( $zone->max_banners ); ?></strong> 
							<?php esc_html_e( 'slots used', 'real-estate-banner-ads' ); ?>
						</div>
					</div>

					<div class="zone-actions">
						<button type="button" class="btn-small btn-edit edit-zone" data-zone-id="<?php echo esc_attr( $zone->id ); ?>">
							<?php esc_html_e( 'Edit', 'real-estate-banner-ads' ); ?>
						</button>
						<button type="button" class="btn-small btn-delete delete-zone" data-zone-id="<?php echo esc_attr( $zone->id ); ?>">
							<?php esc_html_e( 'Delete', 'real-estate-banner-ads' ); ?>
						</button>
					</div>
				</div>
			<?php endforeach; ?>
		<?php else : ?>
			<div class="no-zones">
				<p><?php esc_html_e( 'No ad zones found. Create your first zone to get started!', 'real-estate-banner-ads' ); ?></p>
			</div>
		<?php endif; ?>
	</div>

	<!-- Add/Edit Zone Modal -->
	<div id="add-zone-modal" class="banner-modal" style="display: none;">
		<div class="banner-modal-content">
			<span class="banner-modal-close">&times;</span>
			<h2><?php esc_html_e( 'Add New Zone', 'real-estate-banner-ads' ); ?></h2>
			
			<form method="post" id="zone-form">
				<?php wp_nonce_field( 'zones_action', 'zones_nonce' ); ?>
				<input type="hidden" name="action" value="add_zone">
				
				<table class="banner-form-table">
					<tr>
						<th><label for="zone_id"><?php esc_html_e( 'Zone ID', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label></th>
						<td>
							<input type="text" id="zone_id" name="zone_id" required 
								   pattern="[a-z0-9-_]+" 
								   title="<?php esc_attr_e( 'Only lowercase letters, numbers, hyphens, and underscores allowed', 'real-estate-banner-ads' ); ?>">
							<p class="description"><?php esc_html_e( 'Unique identifier for the zone (lowercase, no spaces)', 'real-estate-banner-ads' ); ?></p>
						</td>
					</tr>
					<tr>
						<th><label for="zone_name"><?php esc_html_e( 'Zone Name', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label></th>
						<td>
							<input type="text" id="zone_name" name="zone_name" required>
						</td>
					</tr>
					<tr>
						<th><label for="zone_description"><?php esc_html_e( 'Description', 'real-estate-banner-ads' ); ?></label></th>
						<td>
							<textarea id="zone_description" name="zone_description" rows="3"></textarea>
						</td>
					</tr>
					<tr>
						<th><label for="max_banners"><?php esc_html_e( 'Maximum Banners', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label></th>
						<td>
							<input type="number" id="max_banners" name="max_banners" min="1" max="10" required value="1">
							<p class="description"><?php esc_html_e( 'Maximum number of banners that can be displayed simultaneously in this zone', 'real-estate-banner-ads' ); ?></p>
						</td>
					</tr>
					<tr>
						<th><label for="dimensions"><?php esc_html_e( 'Recommended Dimensions', 'real-estate-banner-ads' ); ?></label></th>
						<td>
							<input type="text" id="dimensions" name="dimensions" placeholder="728x90">
							<p class="description"><?php esc_html_e( 'Recommended banner size (e.g., 728x90)', 'real-estate-banner-ads' ); ?></p>
						</td>
					</tr>
					<tr>
						<th><label for="active"><?php esc_html_e( 'Active', 'real-estate-banner-ads' ); ?></label></th>
						<td>
							<label>
								<input type="checkbox" id="active" name="active" value="1" checked>
								<?php esc_html_e( 'Zone is active and accepting new banners', 'real-estate-banner-ads' ); ?>
							</label>
						</td>
					</tr>
				</table>

				<p class="submit">
					<input type="submit" class="button-primary" value="<?php esc_attr_e( 'Create Zone', 'real-estate-banner-ads' ); ?>">
					<button type="button" class="button cancel-zone-form"><?php esc_html_e( 'Cancel', 'real-estate-banner-ads' ); ?></button>
				</p>
			</form>
		</div>
	</div>

	<!-- Delete Confirmation Modal -->
	<div id="delete-zone-modal" class="banner-modal">
		<div class="banner-modal-content">
			<span class="banner-modal-close">&times;</span>
			<h2><?php esc_html_e( 'Delete Zone', 'real-estate-banner-ads' ); ?></h2>
			<p><?php esc_html_e( 'Are you sure you want to delete this zone? This action cannot be undone.', 'real-estate-banner-ads' ); ?></p>
			
			<form method="post" id="delete-zone-form">
				<?php wp_nonce_field( 'zones_action', 'zones_nonce' ); ?>
				<input type="hidden" name="action" value="delete_zone">
				<input type="hidden" name="zone_id" id="delete_zone_id">
				
				<p class="submit">
					<input type="submit" class="button-primary" value="<?php esc_attr_e( 'Delete Zone', 'real-estate-banner-ads' ); ?>">
					<button type="button" class="button cancel-delete"><?php esc_html_e( 'Cancel', 'real-estate-banner-ads' ); ?></button>
				</p>
			</form>
		</div>
	</div>
</div>

<style>
.required {
	color: #dc3545;
}

.status-badge {
	padding: 2px 6px;
	border-radius: 3px;
	font-size: 11px;
	font-weight: bold;
	text-transform: uppercase;
}

.status-badge.active {
	background: #d4edda;
	color: #155724;
}

.status-badge.inactive {
	background: #f8d7da;
	color: #721c24;
}

.no-zones {
	text-align: center;
	padding: 40px;
	background: #f8f9fa;
	border-radius: 6px;
	color: #666;
}
</style>

<script>
jQuery(document).ready(function($) {
	// Open add zone modal
	$('[data-modal="add-zone-modal"]').on('click', function(e) {
		e.preventDefault();
		$('#add-zone-modal').show();
	});

	// Delete zone
	$('.delete-zone').on('click', function() {
		var zoneId = $(this).data('zone-id');
		$('#delete_zone_id').val(zoneId);
		$('#delete-zone-modal').show();
	});

	// Cancel buttons
	$('.cancel-zone-form, .cancel-delete').on('click', function() {
		$('.banner-modal').hide();
	});

	// Auto-generate zone ID from name
	$('#zone_name').on('input', function() {
		if (!$('#zone_id').prop('readonly')) {
			var name = $(this).val();
			var id = name.toLowerCase()
				.replace(/[^a-z0-9\s-]/g, '')
				.replace(/\s+/g, '-')
				.replace(/-+/g, '-')
				.trim();
			$('#zone_id').val(id);
		}
	});
});
</script>
