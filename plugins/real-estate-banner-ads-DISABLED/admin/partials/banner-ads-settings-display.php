<?php
/**
 * Provide an admin area view for plugin settings
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/admin/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

// Handle form submission
if ( $_POST && isset( $_POST['submit'] ) && wp_verify_nonce( $_POST['settings_nonce'], 'banner_ads_settings' ) ) {
	// Update settings
	$settings = array(
		'banner_ads_auto_approve' => sanitize_text_field( $_POST['auto_approve'] ?? 'no' ),
		'banner_ads_max_file_size' => intval( $_POST['max_file_size'] ?? 2048 ),
		'banner_ads_allowed_file_types' => array_map( 'sanitize_text_field', $_POST['allowed_file_types'] ?? array( 'jpg', 'jpeg', 'png', 'gif' ) ),
		'banner_ads_email_notifications' => sanitize_text_field( $_POST['email_notifications'] ?? 'yes' ),
		'banner_ads_analytics_retention' => intval( $_POST['analytics_retention'] ?? 365 ),
		'banner_ads_require_approval' => sanitize_text_field( $_POST['require_approval'] ?? 'yes' ),
		'banner_ads_default_link_target' => sanitize_text_field( $_POST['default_link_target'] ?? '_blank' )
	);

	foreach ( $settings as $option_name => $option_value ) {
		update_option( $option_name, $option_value );
	}

	$success_message = __( 'Settings saved successfully!', 'real-estate-banner-ads' );
}

// Get current settings
$auto_approve = get_option( 'banner_ads_auto_approve', 'no' );
$max_file_size = get_option( 'banner_ads_max_file_size', 2048 );
$allowed_file_types = get_option( 'banner_ads_allowed_file_types', array( 'jpg', 'jpeg', 'png', 'gif' ) );
$email_notifications = get_option( 'banner_ads_email_notifications', 'yes' );
$analytics_retention = get_option( 'banner_ads_analytics_retention', 365 );
$require_approval = get_option( 'banner_ads_require_approval', 'yes' );
$default_link_target = get_option( 'banner_ads_default_link_target', '_blank' );
?>

<div class="wrap">
	<h1><?php esc_html_e( 'Banner Ads Settings', 'real-estate-banner-ads' ); ?></h1>

	<?php if ( isset( $success_message ) ) : ?>
		<div class="notice notice-success is-dismissible">
			<p><?php echo esc_html( $success_message ); ?></p>
		</div>
	<?php endif; ?>

	<form method="post" action="">
		<?php wp_nonce_field( 'banner_ads_settings', 'settings_nonce' ); ?>

		<!-- General Settings -->
		<div class="banner-settings-section">
			<h3><?php esc_html_e( 'General Settings', 'real-estate-banner-ads' ); ?></h3>
			
			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Auto-approve Banners', 'real-estate-banner-ads' ); ?></h4>
					<p class="setting-description"><?php esc_html_e( 'Automatically approve uploaded banners without manual review.', 'real-estate-banner-ads' ); ?></p>
				</div>
				<div class="setting-control">
					<select name="auto_approve">
						<option value="no" <?php selected( $auto_approve, 'no' ); ?>><?php esc_html_e( 'No - Require manual approval', 'real-estate-banner-ads' ); ?></option>
						<option value="yes" <?php selected( $auto_approve, 'yes' ); ?>><?php esc_html_e( 'Yes - Auto-approve all banners', 'real-estate-banner-ads' ); ?></option>
					</select>
				</div>
			</div>

			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Require Approval', 'real-estate-banner-ads' ); ?></h4>
					<p class="setting-description"><?php esc_html_e( 'Require admin approval before banners can go live.', 'real-estate-banner-ads' ); ?></p>
				</div>
				<div class="setting-control">
					<select name="require_approval">
						<option value="yes" <?php selected( $require_approval, 'yes' ); ?>><?php esc_html_e( 'Yes - Require approval', 'real-estate-banner-ads' ); ?></option>
						<option value="no" <?php selected( $require_approval, 'no' ); ?>><?php esc_html_e( 'No - Go live immediately', 'real-estate-banner-ads' ); ?></option>
					</select>
				</div>
			</div>

			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Default Link Target', 'real-estate-banner-ads' ); ?></h4>
					<p class="setting-description"><?php esc_html_e( 'Default target for banner links.', 'real-estate-banner-ads' ); ?></p>
				</div>
				<div class="setting-control">
					<select name="default_link_target">
						<option value="_blank" <?php selected( $default_link_target, '_blank' ); ?>><?php esc_html_e( 'New Window (_blank)', 'real-estate-banner-ads' ); ?></option>
						<option value="_self" <?php selected( $default_link_target, '_self' ); ?>><?php esc_html_e( 'Same Window (_self)', 'real-estate-banner-ads' ); ?></option>
					</select>
				</div>
			</div>
		</div>

		<!-- Upload Settings -->
		<div class="banner-settings-section">
			<h3><?php esc_html_e( 'Upload Settings', 'real-estate-banner-ads' ); ?></h3>
			
			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Maximum File Size', 'real-estate-banner-ads' ); ?></h4>
					<p class="setting-description"><?php esc_html_e( 'Maximum file size for banner uploads (in KB).', 'real-estate-banner-ads' ); ?></p>
				</div>
				<div class="setting-control">
					<input type="number" name="max_file_size" value="<?php echo esc_attr( $max_file_size ); ?>" min="512" max="10240" step="256">
					<span class="description"><?php esc_html_e( 'KB (1024 KB = 1 MB)', 'real-estate-banner-ads' ); ?></span>
				</div>
			</div>

			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Allowed File Types', 'real-estate-banner-ads' ); ?></h4>
					<p class="setting-description"><?php esc_html_e( 'File types allowed for banner uploads.', 'real-estate-banner-ads' ); ?></p>
				</div>
				<div class="setting-control">
					<fieldset>
						<label><input type="checkbox" name="allowed_file_types[]" value="jpg" <?php checked( in_array( 'jpg', $allowed_file_types ) ); ?>> JPG</label><br>
						<label><input type="checkbox" name="allowed_file_types[]" value="jpeg" <?php checked( in_array( 'jpeg', $allowed_file_types ) ); ?>> JPEG</label><br>
						<label><input type="checkbox" name="allowed_file_types[]" value="png" <?php checked( in_array( 'png', $allowed_file_types ) ); ?>> PNG</label><br>
						<label><input type="checkbox" name="allowed_file_types[]" value="gif" <?php checked( in_array( 'gif', $allowed_file_types ) ); ?>> GIF</label><br>
						<label><input type="checkbox" name="allowed_file_types[]" value="webp" <?php checked( in_array( 'webp', $allowed_file_types ) ); ?>> WebP</label>
					</fieldset>
				</div>
			</div>
		</div>

		<!-- Email Settings -->
		<div class="banner-settings-section">
			<h3><?php esc_html_e( 'Email Notifications', 'real-estate-banner-ads' ); ?></h3>
			
			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Enable Email Notifications', 'real-estate-banner-ads' ); ?></h4>
					<p class="setting-description"><?php esc_html_e( 'Send email notifications for campaign status changes.', 'real-estate-banner-ads' ); ?></p>
				</div>
				<div class="setting-control">
					<select name="email_notifications">
						<option value="yes" <?php selected( $email_notifications, 'yes' ); ?>><?php esc_html_e( 'Yes - Send notifications', 'real-estate-banner-ads' ); ?></option>
						<option value="no" <?php selected( $email_notifications, 'no' ); ?>><?php esc_html_e( 'No - Disable notifications', 'real-estate-banner-ads' ); ?></option>
					</select>
				</div>
			</div>
		</div>

		<!-- Analytics Settings -->
		<div class="banner-settings-section">
			<h3><?php esc_html_e( 'Analytics Settings', 'real-estate-banner-ads' ); ?></h3>
			
			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Data Retention Period', 'real-estate-banner-ads' ); ?></h4>
					<p class="setting-description"><?php esc_html_e( 'How long to keep analytics data (in days).', 'real-estate-banner-ads' ); ?></p>
				</div>
				<div class="setting-control">
					<select name="analytics_retention">
						<option value="30" <?php selected( $analytics_retention, 30 ); ?>><?php esc_html_e( '30 days', 'real-estate-banner-ads' ); ?></option>
						<option value="90" <?php selected( $analytics_retention, 90 ); ?>><?php esc_html_e( '90 days', 'real-estate-banner-ads' ); ?></option>
						<option value="180" <?php selected( $analytics_retention, 180 ); ?>><?php esc_html_e( '6 months', 'real-estate-banner-ads' ); ?></option>
						<option value="365" <?php selected( $analytics_retention, 365 ); ?>><?php esc_html_e( '1 year', 'real-estate-banner-ads' ); ?></option>
						<option value="730" <?php selected( $analytics_retention, 730 ); ?>><?php esc_html_e( '2 years', 'real-estate-banner-ads' ); ?></option>
						<option value="-1" <?php selected( $analytics_retention, -1 ); ?>><?php esc_html_e( 'Keep forever', 'real-estate-banner-ads' ); ?></option>
					</select>
				</div>
			</div>
		</div>

		<!-- System Information -->
		<div class="banner-settings-section">
			<h3><?php esc_html_e( 'System Information', 'real-estate-banner-ads' ); ?></h3>
			
			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Plugin Version', 'real-estate-banner-ads' ); ?></h4>
				</div>
				<div class="setting-control">
					<code><?php echo esc_html( REAL_ESTATE_BANNER_ADS_VERSION ); ?></code>
				</div>
			</div>

			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Database Version', 'real-estate-banner-ads' ); ?></h4>
				</div>
				<div class="setting-control">
					<code><?php echo esc_html( get_option( 'banner_ads_db_version', '1.0' ) ); ?></code>
				</div>
			</div>

			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'WooCommerce Status', 'real-estate-banner-ads' ); ?></h4>
				</div>
				<div class="setting-control">
					<?php if ( class_exists( 'WooCommerce' ) ) : ?>
						<span class="status-active">✓ <?php esc_html_e( 'Active', 'real-estate-banner-ads' ); ?></span>
						<code><?php echo esc_html( WC()->version ); ?></code>
					<?php else : ?>
						<span class="status-inactive">✗ <?php esc_html_e( 'Not Active', 'real-estate-banner-ads' ); ?></span>
					<?php endif; ?>
				</div>
			</div>

			<?php
			// Get some statistics
			$total_campaigns = wp_count_posts( 'banner_campaign' )->publish;
			$active_campaigns = count( Banner_Campaign::get_by_status( 'active' ) );
			$total_zones = count( Banner_Zone::get_all( false ) );
			
			global $wpdb;
			$impressions_table = $wpdb->prefix . 'banner_impressions';
			$clicks_table = $wpdb->prefix . 'banner_clicks';
			$total_impressions = $wpdb->get_var( "SELECT COUNT(*) FROM $impressions_table" );
			$total_clicks = $wpdb->get_var( "SELECT COUNT(*) FROM $clicks_table" );
			?>

			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Statistics', 'real-estate-banner-ads' ); ?></h4>
				</div>
				<div class="setting-control">
					<ul>
						<li><?php printf( esc_html__( 'Total Campaigns: %d', 'real-estate-banner-ads' ), $total_campaigns ); ?></li>
						<li><?php printf( esc_html__( 'Active Campaigns: %d', 'real-estate-banner-ads' ), $active_campaigns ); ?></li>
						<li><?php printf( esc_html__( 'Total Zones: %d', 'real-estate-banner-ads' ), $total_zones ); ?></li>
						<li><?php printf( esc_html__( 'Total Impressions: %s', 'real-estate-banner-ads' ), number_format( $total_impressions ) ); ?></li>
						<li><?php printf( esc_html__( 'Total Clicks: %s', 'real-estate-banner-ads' ), number_format( $total_clicks ) ); ?></li>
					</ul>
				</div>
			</div>
		</div>

		<!-- Maintenance Actions -->
		<div class="banner-settings-section">
			<h3><?php esc_html_e( 'Maintenance', 'real-estate-banner-ads' ); ?></h3>
			
			<div class="settings-row">
				<div class="setting-label">
					<h4><?php esc_html_e( 'Cleanup Actions', 'real-estate-banner-ads' ); ?></h4>
					<p class="setting-description"><?php esc_html_e( 'Perform maintenance tasks to optimize plugin performance.', 'real-estate-banner-ads' ); ?></p>
				</div>
				<div class="setting-control">
					<button type="button" class="button" id="cleanup-analytics"><?php esc_html_e( 'Cleanup Old Analytics Data', 'real-estate-banner-ads' ); ?></button>
					<button type="button" class="button" id="expire-campaigns"><?php esc_html_e( 'Check Expired Campaigns', 'real-estate-banner-ads' ); ?></button>
				</div>
			</div>
		</div>

		<?php submit_button( __( 'Save Settings', 'real-estate-banner-ads' ) ); ?>
	</form>
</div>

<style>
.status-active {
	color: #28a745;
	font-weight: bold;
}

.status-inactive {
	color: #dc3545;
	font-weight: bold;
}

.setting-control ul {
	margin: 0;
	padding: 0;
	list-style: none;
}

.setting-control li {
	margin-bottom: 5px;
	padding: 5px 0;
	border-bottom: 1px solid #f0f0f0;
}

.setting-control li:last-child {
	border-bottom: none;
}

.setting-control fieldset {
	border: none;
	padding: 0;
	margin: 0;
}

.setting-control fieldset label {
	display: block;
	margin-bottom: 8px;
}

.description {
	color: #666;
	font-style: italic;
	margin-left: 10px;
}
</style>

<script>
jQuery(document).ready(function($) {
	// Cleanup analytics data
	$('#cleanup-analytics').on('click', function() {
		if (confirm('<?php esc_js( __( 'Are you sure you want to cleanup old analytics data? This action cannot be undone.', 'real-estate-banner-ads' ) ); ?>')) {
			var $button = $(this);
			$button.prop('disabled', true).text('<?php esc_js( __( 'Cleaning up...', 'real-estate-banner-ads' ) ); ?>');
			
			$.ajax({
				url: ajaxurl,
				type: 'POST',
				data: {
					action: 'cleanup_analytics_data',
					nonce: '<?php echo wp_create_nonce( 'cleanup_analytics' ); ?>'
				},
				success: function(response) {
					if (response.success) {
						alert('<?php esc_js( __( 'Analytics data cleaned up successfully!', 'real-estate-banner-ads' ) ); ?>');
					} else {
						alert('<?php esc_js( __( 'Error cleaning up data: ', 'real-estate-banner-ads' ) ); ?>' + response.data);
					}
				},
				error: function() {
					alert('<?php esc_js( __( 'Error performing cleanup. Please try again.', 'real-estate-banner-ads' ) ); ?>');
				},
				complete: function() {
					$button.prop('disabled', false).text('<?php esc_js( __( 'Cleanup Old Analytics Data', 'real-estate-banner-ads' ) ); ?>');
				}
			});
		}
	});

	// Check expired campaigns
	$('#expire-campaigns').on('click', function() {
		var $button = $(this);
		$button.prop('disabled', true).text('<?php esc_js( __( 'Checking...', 'real-estate-banner-ads' ) ); ?>');
		
		$.ajax({
			url: ajaxurl,
			type: 'POST',
			data: {
				action: 'check_expired_campaigns',
				nonce: '<?php echo wp_create_nonce( 'check_expired' ); ?>'
			},
			success: function(response) {
				if (response.success) {
					alert(response.data);
				} else {
					alert('<?php esc_js( __( 'Error checking campaigns: ', 'real-estate-banner-ads' ) ); ?>' + response.data);
				}
			},
			error: function() {
				alert('<?php esc_js( __( 'Error checking campaigns. Please try again.', 'real-estate-banner-ads' ) ); ?>');
			},
			complete: function() {
				$button.prop('disabled', false).text('<?php esc_js( __( 'Check Expired Campaigns', 'real-estate-banner-ads' ) ); ?>');
			}
		});
	});
});
</script>
