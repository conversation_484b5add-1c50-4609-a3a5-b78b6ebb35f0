/**
 * Admin JavaScript for Banner Ads
 *
 * @package RealEstateBannerAds
 */

(function($) {
    'use strict';

    /**
     * Banner Ads Admin Object
     */
    var BannerAdsAdmin = {

        /**
         * Initialize admin functionality
         */
        init: function() {
            this.initStatusFilters();
            this.initBulkActions();
            this.initModals();
            this.initFormValidation();
            this.initMediaUploader();
        },

        /**
         * Initialize media uploader functionality
         */
        initMediaUploader: function() {
            // Prevent multiple initializations
            if (window.bannerMediaUploaderInitialized) {
                return;
            }
            window.bannerMediaUploaderInitialized = true;

            var self = this;

            // Media uploader for campaign edit pages
            $(document).off('click', '#upload_image_button').on('click', '#upload_image_button', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var button = $(this);

                // Check if wp.media is available
                if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
                    alert('WordPress media library is not available. Please refresh the page and try again.');
                    return;
                }

                // Prevent multiple clicks
                if (button.hasClass('uploading')) {
                    return;
                }

                self.openMediaUploader(button);
            });
        },

        /**
         * Open WordPress media uploader
         */
        openMediaUploader: function(button) {
            var originalText = button.text();
            button.addClass('uploading').text('Loading...');

            try {
                // Create media uploader instance
                var mediaUploader = wp.media({
                    title: 'Choose Banner Image',
                    button: {
                        text: 'Choose Image'
                    },
                    multiple: false,
                    library: {
                        type: 'image'
                    }
                });

                // Handle image selection
                mediaUploader.on('select', function() {
                    try {
                        var attachment = mediaUploader.state().get('selection').first().toJSON();
                        $('#banner_image_url').val(attachment.url);
                        $('#image_preview').html('<img src="' + attachment.url + '" style="max-width: 300px; max-height: 150px; border-radius: 4px;">');
                    } catch (error) {
                        console.error('Error selecting image:', error);
                        alert('Error selecting image. Please try again.');
                    }
                });

                // Handle close event
                mediaUploader.on('close', function() {
                    button.removeClass('uploading').text(originalText);
                });

                // Open the uploader
                mediaUploader.open();

            } catch (error) {
                console.error('Error opening media uploader:', error);
                button.removeClass('uploading').text(originalText);
                alert('Error opening media library. Please refresh the page and try again.');
            }
        },

        /**
         * Initialize status filters
         */
        initStatusFilters: function() {
            $('#status-filter').on('change', function() {
                var status = $(this).val();
                var url = new URL(window.location);
                
                if (status) {
                    url.searchParams.set('status', status);
                } else {
                    url.searchParams.delete('status');
                }
                
                window.location = url;
            });
        },

        /**
         * Initialize bulk actions
         */
        initBulkActions: function() {
            // Select all checkbox
            $('#cb-select-all-1').on('change', function() {
                $('input[name="post[]"]').prop('checked', this.checked);
            });

            // Individual checkboxes
            $('input[name="post[]"]').on('change', function() {
                var allChecked = $('input[name="post[]"]:checked').length === $('input[name="post[]"]').length;
                $('#cb-select-all-1').prop('checked', allChecked);
            });
        },

        /**
         * Initialize modals
         */
        initModals: function() {
            // Open modal for adding a new zone
            $(document).on('click', '.page-title-action[data-modal="add-zone-modal"]', function(e) {
                e.preventDefault();
                var modalId = $(this).data('modal');
                
                // Clear form for new zone
                $('#zone-form')[0].reset();
                $('#zone-id').prop('readonly', false);
                $('#zone-form input[name="action"]').val('add_zone');
                $('#' + modalId + ' h2').text('Add New Zone');
                $('#' + modalId + ' .button-primary').val('Create Zone');
                
                $('#' + modalId).show();
            });

            // Close modal
            $(document).on('click', '.banner-modal-close, .banner-modal, .cancel-zone-form', function(e) {
                if (e.target === this || $(this).hasClass('cancel-zone-form') || $(this).hasClass('banner-modal-close')) {
                    e.preventDefault();
                    $('.banner-modal').hide();
                }
            });

            // Escape key to close modal
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27) {
                    $('.banner-modal').hide();
                }
            });
        },

        /**
         * Initialize form validation
         */
        initFormValidation: function() {
            // Zone form validation
            $(document).on('submit', '#zone-form', function(e) {
                var zoneId = $('#zone-id').val().trim();
                var zoneName = $('#zone-name').val().trim();
                var maxBanners = $('#max-banners').val().trim();

                if (!zoneId || !zoneName || !maxBanners) {
                    e.preventDefault();
                    alert('Please fill in all required fields.');
                    return false;
                }

                // Validate zone ID format
                if (!/^[a-z0-9-_]+$/.test(zoneId)) {
                    e.preventDefault();
                    alert('Zone ID can only contain lowercase letters, numbers, hyphens, and underscores.');
                    return false;
                }
            });

            // Campaign form validation
            $('#campaign-form').on('submit', function(e) {
                var title = $('#campaign-title').val();
                var bannerType = $('input[name="banner_type"]:checked').val();
                var zone = $('#campaign-zone').val();

                if (!title || !bannerType || !zone) {
                    e.preventDefault();
                    alert('Please fill in all required fields.');
                    return false;
                }

                if (bannerType === 'image') {
                    var imageUrl = $('#banner-image-url').val();
                    if (!imageUrl) {
                        e.preventDefault();
                        alert('Please provide an image URL for image banners.');
                        return false;
                    }
                } else if (bannerType === 'html') {
                    var htmlContent = $('#banner-html-content').val();
                    if (!htmlContent) {
                        e.preventDefault();
                        alert('Please provide HTML content for HTML banners.');
                        return false;
                    }
                }
            });
        }
    };

    /**
     * Zone Management
     */
    var ZoneManager = {
        
        /**
         * Initialize zone management
         */
        init: function() {
            this.initZoneActions();
            this.initZoneForm();
        },

        /**
         * Initialize zone actions
         */
        initZoneActions: function() {
            // Delete zone
            $(document).on('click', '.delete-zone', function(e) {
                e.preventDefault();
                
                if (confirm('Are you sure you want to delete this zone? This action cannot be undone.')) {
                    var zoneId = $(this).data('zone-id');
                    
                    $.ajax({
                        url: banner_ads_admin_ajax.ajax_url,
                        type: 'POST',
                        data: {
                            action: 'delete_banner_zone',
                            zone_id: zoneId,
                            nonce: banner_ads_admin_ajax.nonce
                        },
                        success: function(response) {
                            if (response.success) {
                                location.reload();
                            } else {
                                alert('Error deleting zone: ' + response.data);
                            }
                        },
                        error: function() {
                            alert('Error deleting zone. Please try again.');
                        }
                    });
                }
            });

            // Edit zone
            $(document).on('click', '.edit-zone', function(e) {
                e.preventDefault();
                var zoneId = $(this).data('zone-id');
                
                $.ajax({
                    url: banner_ads_admin_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'get_banner_zone',
                        zone_id: zoneId,
                        nonce: banner_ads_admin_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            var zone = response.data;
                            // Populate form
                            $('#zone_id').val(zone.id).prop('readonly', true);
                            $('#zone_name').val(zone.name);
                            $('#zone_description').val(zone.description);
                            $('#max_banners').val(zone.max_banners);
                            $('#dimensions').val(zone.dimensions);
                            $('#active').prop('checked', zone.active);
                            
                            // Update modal for editing
                            $('#zone-form input[name="action"]').val('update_zone');
                            $('#add-zone-modal h2').text('Edit Zone');
                            $('#add-zone-modal .button-primary').val('Update Zone');
                            
                            // Show modal
                            $('#add-zone-modal').show();
                        } else {
                            alert('Error: ' + response.data);
                        }
                    },
                    error: function() {
                        alert('An error occurred while fetching zone data.');
                    }
                });
            });
        },

        /**
         * Initialize zone form
         */
        initZoneForm: function() {
            // Auto-generate zone ID from name
            $('#zone-name').on('input', function() {
                var name = $(this).val();
                var id = name.toLowerCase()
                    .replace(/[^a-z0-9\s-]/g, '')
                    .replace(/\s+/g, '-')
                    .replace(/-+/g, '-')
                    .trim();
                
                $('#zone-id').val(id);
            });
        }
    };

    /**
     * Analytics Dashboard
     */
    var AnalyticsDashboard = {
        
        /**
         * Initialize analytics
         */
        init: function() {
            this.initDateFilters();
            this.loadCharts();
        },

        /**
         * Initialize date filters
         */
        initDateFilters: function() {
            $('#analytics-date-range').on('change', function() {
                var range = $(this).val();
                AnalyticsDashboard.loadCharts(range);
            });
        },

        /**
         * Load analytics charts
         */
        loadCharts: function(dateRange) {
            dateRange = dateRange || '30days';
            
            $.ajax({
                url: banner_ads_admin_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_analytics_data',
                    date_range: dateRange,
                    nonce: banner_ads_admin_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        // Update charts with new data
                        // Implementation would depend on chosen chart library
                        console.log('Analytics data:', response.data);
                    }
                },
                error: function() {
                    console.log('Error loading analytics data');
                }
            });
        }
    };

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        BannerAdsAdmin.init();
        
        // Initialize specific modules based on current page
        if ($('.banner-zones-page').length) {
            ZoneManager.init();
        }
        
        if ($('.analytics-page').length) {
            AnalyticsDashboard.init();
        }
    });

})(jQuery);
