/**
 * Admin CSS for Banner Ads
 *
 * @package RealEstateBannerAds
 */

/* Admin Dashboard Styles */
.banner-ads-stats {
    margin: 20px 0;
}

.banner-ads-stat-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.banner-ads-stat-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.banner-ads-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.banner-ads-stat-card h3 {
    font-size: 36px;
    margin: 0 0 10px 0;
    color: #333;
}

.banner-ads-stat-card p {
    margin: 0;
    color: #666;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.banner-ads-stat-card.active h3 {
    color: #28a745;
}

.banner-ads-stat-card.pending h3 {
    color: #ffc107;
}

.banner-ads-stat-card.expired h3 {
    color: #dc3545;
}

/* Status Badges */
.banner-status {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.banner-status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.banner-status-approved {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.banner-status-active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.banner-status-expired {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.banner-status-rejected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Performance Metrics */
.banner-performance {
    font-size: 12px;
    line-height: 1.4;
}

.banner-performance div {
    margin-bottom: 2px;
}

/* Zone Management */
.banner-zones-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.banner-zone-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.banner-zone-card h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 18px;
}

.zone-description {
    color: #666;
    margin-bottom: 15px;
    font-size: 14px;
}

.zone-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 13px;
}

.zone-capacity {
    background: #f8f9fa;
    padding: 8px 12px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.zone-capacity.full {
    background: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.zone-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 3px;
    text-decoration: none;
    border: 1px solid;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-edit {
    background: #0073aa;
    color: white;
    border-color: #0073aa;
}

.btn-edit:hover {
    background: #005a87;
    border-color: #005a87;
    color: white;
}

.btn-delete {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

.btn-delete:hover {
    background: #c82333;
    border-color: #bd2130;
    color: white;
}

/* Analytics Charts */
.analytics-container {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    margin: 20px 0;
}

.analytics-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.analytics-filters {
    display: flex;
    gap: 10px;
    align-items: center;
}

.analytics-filters select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.chart-container {
    position: relative;
    height: 400px;
    margin: 20px 0;
}

.analytics-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.summary-item {
    text-align: center;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #e9ecef;
}

.summary-number {
    font-size: 24px;
    font-weight: bold;
    color: #0073aa;
    display: block;
}

.summary-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
    margin-top: 5px;
}

/* Settings Page */
.banner-settings-section {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    margin: 20px 0;
}

.banner-settings-section h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.settings-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.settings-row:last-child {
    border-bottom: none;
}

.setting-label {
    flex: 1;
    margin-right: 20px;
}

.setting-label h4 {
    margin: 0 0 5px 0;
    color: #333;
}

.setting-description {
    font-size: 13px;
    color: #666;
    margin: 0;
}

.setting-control {
    flex: 0 0 auto;
}

.setting-control input,
.setting-control select {
    padding: 6px 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

/* Form Styles */
.banner-form-table {
    width: 100%;
    border-collapse: collapse;
}

.banner-form-table th,
.banner-form-table td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.banner-form-table th {
    background: #f8f9fa;
    font-weight: bold;
    width: 200px;
}

.banner-form-table input,
.banner-form-table select,
.banner-form-table textarea {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.banner-form-table textarea {
    min-height: 100px;
    resize: vertical;
}

/* Modal Styles */
.banner-modal {
    display: none;
    position: fixed;
    z-index: 100000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.banner-modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    border-radius: 6px;
    width: 80%;
    max-width: 600px;
    position: relative;
}

.banner-modal-close {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    cursor: pointer;
    color: #999;
}

.banner-modal-close:hover {
    color: #333;
}

/* Responsive Design */
@media (max-width: 768px) {
    .banner-ads-stat-cards {
        grid-template-columns: 1fr;
    }
    
    .banner-zones-grid {
        grid-template-columns: 1fr;
    }
    
    .analytics-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
    
    .analytics-filters {
        width: 100%;
        justify-content: flex-start;
    }
    
    .settings-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .setting-control {
        width: 100%;
    }
    
    .setting-control input,
    .setting-control select {
        width: 100%;
    }
}

/* Loading States */
.banner-loading {
    position: relative;
    opacity: 0.6;
    pointer-events: none;
}

.banner-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #0073aa;
    border-radius: 50%;
    animation: banner-spin 1s linear infinite;
}

@keyframes banner-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notifications */
.banner-notice {
    padding: 12px 15px;
    margin: 15px 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.banner-notice.notice-success {
    background: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.banner-notice.notice-error {
    background: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.banner-notice.notice-warning {
    background: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.banner-notice.notice-info {
    background: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}
