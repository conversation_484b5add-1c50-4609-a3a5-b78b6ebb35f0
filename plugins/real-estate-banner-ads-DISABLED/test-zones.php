<?php
/**
 * Test script to check and create zones
 */

// WordPress environment
require_once('../../../wp-config.php');

// Include the Banner_Zone class
require_once('models/class-banner-zone.php');

echo "<h1>Banner Zones Test</h1>";

// Check current zones
$zones = get_option('banner_ads_zones', array());
echo "<h2>Current Zones in Database:</h2>";
if (empty($zones)) {
    echo "<p>No zones found in database!</p>";
    
    // Create default zones
    echo "<h2>Creating Default Zones...</h2>";
    $default_zones = array(
        'homepage-header' => array(
            'name' => 'Homepage Header',
            'description' => 'Banner area at the top of the homepage',
            'max_banners' => 3,
            'dimensions' => '728x90',
            'active' => true
        ),
        'sidebar' => array(
            'name' => 'Sidebar',
            'description' => 'Sidebar banner area',
            'max_banners' => 2,
            'dimensions' => '300x250',
            'active' => true
        ),
        'category-header' => array(
            'name' => 'Category Pages Header',
            'description' => 'Banner area on category pages',
            'max_banners' => 2,
            'dimensions' => '728x90',
            'active' => true
        ),
        'footer' => array(
            'name' => 'Footer',
            'description' => 'Footer banner area',
            'max_banners' => 4,
            'dimensions' => '300x100',
            'active' => true
        )
    );
    
    $result = update_option('banner_ads_zones', $default_zones);
    if ($result) {
        echo "<p style='color: green;'>✓ Default zones created successfully!</p>";
    } else {
        echo "<p style='color: red;'>✗ Failed to create default zones.</p>";
    }
    
    // Refresh zones
    $zones = get_option('banner_ads_zones', array());
} else {
    echo "<ul>";
    foreach ($zones as $zone_id => $zone_data) {
        $active = $zone_data['active'] ? 'Active' : 'Inactive';
        echo "<li><strong>{$zone_id}</strong>: {$zone_data['name']} ({$active})</li>";
    }
    echo "</ul>";
}

// Test Banner_Zone::get_options()
echo "<h2>Banner_Zone::get_options() Test:</h2>";
$options = Banner_Zone::get_options(true);
if (empty($options)) {
    echo "<p style='color: red;'>✗ No zone options returned!</p>";
} else {
    echo "<ul>";
    foreach ($options as $zone_id => $zone_name) {
        echo "<li><strong>{$zone_id}</strong>: {$zone_name}</li>";
    }
    echo "</ul>";
}

// Test Banner_Zone::get_all()
echo "<h2>Banner_Zone::get_all() Test:</h2>";
$all_zones = Banner_Zone::get_all(true);
if (empty($all_zones)) {
    echo "<p style='color: red;'>✗ No zones returned from get_all()!</p>";
} else {
    echo "<ul>";
    foreach ($all_zones as $zone) {
        echo "<li><strong>{$zone->id}</strong>: {$zone->name} (Max: {$zone->max_banners}, Active: " . ($zone->active ? 'Yes' : 'No') . ")</li>";
    }
    echo "</ul>";
}

echo "<h2>Test Complete</h2>";
echo "<p><a href='http://ads-manager.local/my-account/upload-banner/'>Go to Upload Page</a></p>";
?>
