# Real Estate Banner Ads Manager

A comprehensive WordPress plugin that allows real estate agents to purchase and manage banner ads on your site. This includes uploading banners, choosing packages, making payments, and viewing performance analytics.

## Features

- **Self-Service Ad Upload System**: Upload image or HTML banner ads
- **WooCommerce Integration**: Purchase ad packages with automated activation
- **Ad Zone Management**: Admin-defined zones with capacity limits
- **Campaign Scheduling**: Automated start/end dates based on package duration
- **Analytics & Tracking**: Real-time impression and click tracking
- **Client Dashboard**: Self-service management and performance viewing
- **Admin Panel**: Complete campaign and zone management
- **Email Notifications**: Automated notifications for campaign status changes

## Requirements

- WordPress 6.0 or higher
- WooCommerce 7.0 or higher
- PHP 7.4 or higher
- MySQL 5.7 or higher

## Installation

1. Upload the plugin files to `/wp-content/plugins/real-estate-banner-ads/`
2. Activate the plugin through the 'Plugins' screen in WordPress
3. Ensure WooCommerce is installed and activated
4. Configure the plugin settings under 'Banner Ads' in the admin menu

## Initial Setup

### 1. Configure Ad Zones

1. Go to **Banner Ads > Ad Zones** in your WordPress admin
2. Review and customize the default zones:
   - Homepage Header (728x90)
   - Sidebar (300x250)
   - Category Pages Header (728x90)
   - Footer (300x100)
3. Add new zones as needed for your site layout

### 2. Review Banner Products

The plugin automatically creates three WooCommerce products:
- Banner Ad - 1 Week ($99)
- Banner Ad - 1 Month ($299)
- Banner Ad - 3 Months ($799)

You can modify prices and descriptions in **Products > All Products**.

### 3. Add Shortcodes to Your Site

#### Display Banner Zones
```php
[banner_ads_zone zone="homepage-header" limit="3"]
```

#### Client Dashboard
```php
[banner_ads_dashboard]
```

#### Upload Form
```php
[banner_ads_upload]
```

### 4. Configure Settings

Go to **Banner Ads > Settings** to configure:
- Auto-approval settings
- File upload limits
- Email notifications
- Analytics retention

## Usage

### For Site Administrators

#### Managing Campaigns
1. View all campaigns in **Banner Ads > All Campaigns**
2. Filter by status (pending, active, expired, etc.)
3. Approve/reject campaigns as needed
4. View performance analytics

#### Managing Zones
1. Create new ad zones in **Banner Ads > Ad Zones**
2. Set capacity limits and dimensions
3. Enable/disable zones as needed

#### Analytics
1. View comprehensive analytics in **Banner Ads > Analytics**
2. Filter by date range and campaign
3. Export reports (if needed)

### For Clients (Real Estate Agents)

#### Purchasing Ad Space
1. Visit your shop page to browse ad packages
2. Purchase desired package duration
3. Upload banner ad using the upload form
4. Wait for approval and automatic activation

#### Managing Campaigns
1. Access the client dashboard via shortcode
2. View campaign performance metrics
3. Track impressions, clicks, and CTR
4. Monitor campaign status and dates

#### Uploading Banners
1. Use the upload form (via shortcode)
2. Choose between image or HTML banners
3. Select target ad zone
4. Provide link URL and targeting options

## Shortcode Reference

### Banner Zone Display
```php
[banner_ads_zone zone="zone-id" limit="3" class="custom-class"]
```

**Parameters:**
- `zone`: Zone identifier (required)
- `limit`: Maximum banners to display (default: 3)
- `class`: Additional CSS class

### Client Dashboard
```php
[banner_ads_dashboard]
```

Shows campaign overview, performance metrics, and management options.

### Upload Form
```php
[banner_ads_upload]
```

Displays the banner upload form for logged-in users.

## Template Integration

### Adding Banner Zones to Your Theme

```php
// In your theme files
if (function_exists('do_shortcode')) {
    echo do_shortcode('[banner_ads_zone zone="homepage-header"]');
}
```

### Custom Zone Styling

Add custom CSS to style banner zones:

```css
.banner-ads-zone-homepage-header {
    text-align: center;
    margin: 20px 0;
}

.banner-ads-zone-sidebar {
    margin-bottom: 20px;
}

.banner-ad {
    transition: transform 0.3s ease;
}

.banner-ad:hover {
    transform: scale(1.05);
}
```

## Hooks and Filters

### Actions
- `banner_ads_campaign_activated`: Fired when a campaign is activated
- `banner_ads_campaign_expired`: Fired when a campaign expires
- `banner_ads_impression_tracked`: Fired when an impression is tracked
- `banner_ads_click_tracked`: Fired when a click is tracked

### Filters
- `banner_ads_allowed_file_types`: Modify allowed file types for uploads
- `banner_ads_max_file_size`: Modify maximum file size
- `banner_ads_zone_capacity`: Modify zone capacity limits

## Troubleshooting

### Common Issues

#### Plugin Not Activating
- Ensure WooCommerce is installed and activated
- Check PHP version compatibility (7.4+)
- Verify file permissions

#### Banners Not Displaying
- Check if campaigns are active and within date range
- Verify zone shortcodes are correctly placed
- Ensure JavaScript is enabled for tracking

#### Upload Issues
- Check file size limits (default 2MB)
- Verify allowed file types (JPG, PNG, GIF)
- Ensure proper file permissions

#### Analytics Not Tracking
- Verify JavaScript is loading correctly
- Check for JavaScript errors in browser console
- Ensure AJAX endpoints are accessible

### Debug Mode

Enable WordPress debug mode to troubleshoot issues:

```php
// In wp-config.php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Support

For support and feature requests, please contact the plugin developer or submit issues through the appropriate channels.

## Changelog

### Version 1.0.0
- Initial release
- Complete banner ads management system
- WooCommerce integration
- Analytics tracking
- Client dashboard
- Admin management interface

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed for real estate banner advertising management with WordPress and WooCommerce integration.
