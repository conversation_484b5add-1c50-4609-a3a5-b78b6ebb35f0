<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .file-upload-area {
            border: 2px dashed #ddd;
            border-radius: 6px;
            padding: 40px 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            margin-top: 10px;
            position: relative;
            user-select: none;
        }
        .file-upload-area:hover {
            border-color: #0073aa;
            background: #f0f8ff;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
        }
        .upload-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        .upload-text strong {
            color: #0073aa;
        }
        #fallback-upload-btn {
            background: #0073aa;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 12px 24px;
            font-size: 14px;
            cursor: pointer;
            margin-top: 15px;
        }
        .file-info {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            display: none;
        }
    </style>
</head>
<body>
    <h1>Upload Test Page</h1>
    <p>This is a simple test to verify the upload functionality works correctly.</p>
    
    <div class="file-upload-area" id="file-upload-area">
        <div class="upload-content">
            <div class="upload-icon">📁</div>
            <div class="upload-text">
                <strong>Click to upload</strong> or drag and drop
            </div>
            <div class="upload-requirements">
                <small>Maximum file size: 2MB. Supported formats: JPG, PNG, GIF</small>
            </div>
        </div>
        <input type="file" id="banner_image" name="banner_image" accept="image/*" style="display: none;">
        <button type="button" id="fallback-upload-btn" style="display: none;">
            Choose File
        </button>
    </div>
    
    <div class="file-info" id="file-info">
        <div class="file-preview"></div>
        <div class="file-details">
            <div class="file-name"></div>
            <div class="file-size"></div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            console.log('Test page ready');
            
            // Show fallback button after 1 second
            setTimeout(function() {
                $('#fallback-upload-btn').show();
            }, 1000);
            
            // File upload click handler
            $(document).on('click', '#file-upload-area, #fallback-upload-btn', function(e) {
                console.log('Upload area clicked');
                e.preventDefault();
                e.stopPropagation();

                var fileInput = $('#banner_image')[0];
                if (fileInput) {
                    // Create a new file input
                    var newInput = document.createElement('input');
                    newInput.type = 'file';
                    newInput.accept = 'image/*';
                    newInput.style.position = 'absolute';
                    newInput.style.left = '-9999px';
                    newInput.style.top = '-9999px';
                    newInput.style.opacity = '0';
                    
                    document.body.appendChild(newInput);
                    
                    $(newInput).on('change', function() {
                        if (this.files && this.files[0]) {
                            var dt = new DataTransfer();
                            dt.items.add(this.files[0]);
                            fileInput.files = dt.files;
                            $(fileInput).trigger('change');
                        }
                        document.body.removeChild(newInput);
                    });
                    
                    newInput.click();
                }
            });
            
            // File change handler
            $(document).on('change', '#banner_image', function() {
                console.log('File selected:', this.files);
                $('#fallback-upload-btn').hide();
                
                var file = this.files[0];
                if (file) {
                    var fileSize = (file.size / 1024 / 1024).toFixed(2);
                    
                    $('#file-info').show();
                    $('#file-info .file-name').text(file.name);
                    $('#file-info .file-size').text(fileSize + ' MB');
                    
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('#file-info .file-preview').html('<img src="' + e.target.result + '" style="max-width: 200px; max-height: 100px; border-radius: 4px;">');
                    };
                    reader.readAsDataURL(file);
                }
            });
            
            // Drag and drop
            $(document).on('dragover', '#file-upload-area', function(e) {
                e.preventDefault();
                $(this).addClass('dragover');
            });

            $(document).on('dragleave', '#file-upload-area', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
            });

            $(document).on('drop', '#file-upload-area', function(e) {
                e.preventDefault();
                $(this).removeClass('dragover');
                
                var files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    $('#banner_image')[0].files = files;
                    $('#banner_image').trigger('change');
                }
            });
        });
    </script>
</body>
</html>
