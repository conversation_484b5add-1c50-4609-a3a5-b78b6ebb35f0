<?php
/**
 * Plugin Name: Real Estate Banner Ads Manager
 * Plugin URI: https://example.com/real-estate-banner-ads
 * Description: A comprehensive banner ads management system for real estate agents with WooCommerce integration, analytics tracking, and self-service ad upload capabilities.
 * Version: 1.0.0
 * Author: Your Company
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: real-estate-banner-ads
 * Domain Path: /languages
 * Requires at least: 6.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * WC requires at least: 7.0
 * WC tested up to: 9.9
 *
 * @package RealEstateBannerAds
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

/**
 * Currently plugin version.
 */
define( 'REAL_ESTATE_BANNER_ADS_VERSION', '1.0.0' );

/**
 * Plugin directory path.
 */
define( 'REAL_ESTATE_BANNER_ADS_PATH', plugin_dir_path( __FILE__ ) );

/**
 * Plugin directory URL.
 */
define( 'REAL_ESTATE_BANNER_ADS_URL', plugin_dir_url( __FILE__ ) );

/**
 * Plugin basename.
 */
define( 'REAL_ESTATE_BANNER_ADS_BASENAME', plugin_basename( __FILE__ ) );

/**
 * The code that runs during plugin activation.
 */
function activate_real_estate_banner_ads() {
	require_once REAL_ESTATE_BANNER_ADS_PATH . 'includes/class-banner-ads-activator.php';
	Banner_Ads_Activator::activate();
}

/**
 * The code that runs during plugin deactivation.
 */
function deactivate_real_estate_banner_ads() {
	require_once REAL_ESTATE_BANNER_ADS_PATH . 'includes/class-banner-ads-deactivator.php';
	Banner_Ads_Deactivator::deactivate();
}

register_activation_hook( __FILE__, 'activate_real_estate_banner_ads' );
register_deactivation_hook( __FILE__, 'deactivate_real_estate_banner_ads' );

/**
 * Check if WooCommerce is active
 */
function real_estate_banner_ads_check_woocommerce() {
	if ( ! class_exists( 'WooCommerce' ) ) {
		add_action( 'admin_notices', 'real_estate_banner_ads_woocommerce_notice' );
		return false;
	}
	return true;
}

/**
 * Display admin notice if WooCommerce is not active
 */
function real_estate_banner_ads_woocommerce_notice() {
	?>
	<div class="notice notice-error">
		<p><?php esc_html_e( 'Real Estate Banner Ads Manager requires WooCommerce to be installed and active.', 'real-estate-banner-ads' ); ?></p>
	</div>
	<?php
}

/**
 * The core plugin class that is used to define internationalization,
 * admin-specific hooks, and public-facing site hooks.
 */
require REAL_ESTATE_BANNER_ADS_PATH . 'includes/class-banner-ads.php';

/**
 * Begins execution of the plugin.
 */
function run_real_estate_banner_ads() {
	if ( ! real_estate_banner_ads_check_woocommerce() ) {
		return;
	}

	$plugin = new Banner_Ads();
	$plugin->run();
}

/**
 * Initialize the plugin after all plugins are loaded
 */
add_action( 'plugins_loaded', 'run_real_estate_banner_ads' );

/**
 * Declare WooCommerce HPOS compatibility
 */
add_action( 'before_woocommerce_init', function() {
	if ( class_exists( \Automattic\WooCommerce\Utilities\FeaturesUtil::class ) ) {
		\Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', __FILE__, true );
	}
});

/**
 * Debug function - Remove after testing
 */
add_action( 'wp_footer', function() {
	if ( current_user_can( 'manage_options' ) && isset( $_GET['debug_banners'] ) ) {
		$campaigns = get_posts( array(
			'post_type' => 'banner_campaign',
			'posts_per_page' => -1,
			'post_status' => 'publish'
		));

		echo '<div style="background: white; padding: 20px; margin: 20px; border: 2px solid #0073aa; position: fixed; top: 50px; right: 20px; width: 400px; z-index: 9999; max-height: 400px; overflow-y: auto;">';
		echo '<h3>🔍 Debug: Banner Campaigns</h3>';
		echo '<p><strong>Total Campaigns:</strong> ' . count( $campaigns ) . '</p>';

		foreach ( $campaigns as $campaign ) {
			$status = get_post_meta( $campaign->ID, '_campaign_status', true );
			$zone = get_post_meta( $campaign->ID, '_banner_zone', true );
			$start = get_post_meta( $campaign->ID, '_campaign_start_date', true );
			$end = get_post_meta( $campaign->ID, '_campaign_end_date', true );
			$author = get_user_by( 'id', $campaign->post_author );

			echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
			echo "<strong>{$campaign->post_title}</strong><br>";
			echo "ID: {$campaign->ID}<br>";
			echo "Status: <span style='color: " . ($status === 'active' ? 'green' : 'orange') . ";'>{$status}</span><br>";
			echo "Zone: {$zone}<br>";
			echo "Author: " . ($author ? $author->display_name : 'Unknown') . " (ID: {$campaign->post_author})<br>";
			echo "Start: {$start}<br>";
			echo "End: {$end}<br>";
			echo "</div>";
		}

		echo '<p><small>Add ?debug_banners=1 to any page URL to see this debug info</small></p>';
		echo '</div>';
	}
});
