<?php
/**
 * Banner Zone model class
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/models
 */

/**
 * Banner Zone model.
 *
 * Handles banner zone data and operations.
 */
class Banner_Zone {

	/**
	 * Zone ID.
	 *
	 * @var string
	 */
	public $id;

	/**
	 * Zone name.
	 *
	 * @var string
	 */
	public $name;

	/**
	 * Zone description.
	 *
	 * @var string
	 */
	public $description;

	/**
	 * Maximum number of banners allowed in this zone.
	 *
	 * @var int
	 */
	public $max_banners;

	/**
	 * Recommended dimensions for banners in this zone.
	 *
	 * @var string
	 */
	public $dimensions;

	/**
	 * Whether the zone is active.
	 *
	 * @var bool
	 */
	public $active;

	/**
	 * Constructor.
	 *
	 * @param string $zone_id Zone ID.
	 */
	public function __construct( $zone_id = '' ) {
		if ( $zone_id ) {
			$this->load( $zone_id );
		}
	}

	/**
	 * Load zone data.
	 *
	 * @param string $zone_id Zone ID.
	 * @return bool True on success, false on failure.
	 */
	public function load( $zone_id ) {
		$zones = get_option( 'banner_ads_zones', array() );
		
		if ( ! isset( $zones[ $zone_id ] ) ) {
			return false;
		}

		$zone_data = $zones[ $zone_id ];
		
		$this->id = $zone_id;
		$this->name = $zone_data['name'] ?? '';
		$this->description = $zone_data['description'] ?? '';
		$this->max_banners = intval( $zone_data['max_banners'] ?? 1 );
		$this->dimensions = $zone_data['dimensions'] ?? '';
		$this->active = $zone_data['active'] ?? true;

		return true;
	}

	/**
	 * Save zone data.
	 *
	 * @return bool True on success, false on failure.
	 */
	public function save() {
		if ( empty( $this->id ) ) {
			return false;
		}

		$zones = get_option( 'banner_ads_zones', array() );
		
		$zones[ $this->id ] = array(
			'name' => $this->name,
			'description' => $this->description,
			'max_banners' => $this->max_banners,
			'dimensions' => $this->dimensions,
			'active' => $this->active
		);

		return update_option( 'banner_ads_zones', $zones );
	}

	/**
	 * Delete zone.
	 *
	 * @return bool True on success, false on failure.
	 */
	public function delete() {
		if ( empty( $this->id ) ) {
			return false;
		}

		$zones = get_option( 'banner_ads_zones', array() );
		
		if ( isset( $zones[ $this->id ] ) ) {
			unset( $zones[ $this->id ] );
			return update_option( 'banner_ads_zones', $zones );
		}

		return false;
	}

	/**
	 * Get active campaigns in this zone.
	 *
	 * @return array Array of campaign IDs.
	 */
	public function get_active_campaigns() {
		$args = array(
			'post_type' => 'banner_campaign',
			'post_status' => 'publish',
			'posts_per_page' => -1,
			'meta_query' => array(
				'relation' => 'AND',
				array(
					'key' => '_banner_zone',
					'value' => $this->id,
					'compare' => '='
				),
				array(
					'key' => '_campaign_status',
					'value' => 'active',
					'compare' => '='
				)
			),
			'fields' => 'ids'
		);

		return get_posts( $args );
	}

	/**
	 * Check if zone has available slots.
	 *
	 * @return bool True if slots available, false otherwise.
	 */
	public function has_available_slots() {
		$active_campaigns = $this->get_active_campaigns();
		return count( $active_campaigns ) < $this->max_banners;
	}

	/**
	 * Get number of available slots.
	 *
	 * @return int Number of available slots.
	 */
	public function get_available_slots() {
		$active_campaigns = $this->get_active_campaigns();
		return max( 0, $this->max_banners - count( $active_campaigns ) );
	}

	/**
	 * Get all zones.
	 *
	 * @param bool $active_only Whether to return only active zones.
	 * @return array Array of Banner_Zone objects.
	 */
	public static function get_all( $active_only = true ) {
		$zones_data = get_option( 'banner_ads_zones', array() );
		$zones = array();

		foreach ( $zones_data as $zone_id => $zone_data ) {
			if ( $active_only && ! ( $zone_data['active'] ?? true ) ) {
				continue;
			}

			$zone = new self();
			if ( $zone->load( $zone_id ) ) {
				$zones[] = $zone;
			}
		}

		return $zones;
	}

	/**
	 * Get zones as options array for select fields.
	 *
	 * @param bool $active_only Whether to return only active zones.
	 * @return array Array of zone options (id => name).
	 */
	public static function get_options( $active_only = true ) {
		$zones = self::get_all( $active_only );
		$options = array();

		foreach ( $zones as $zone ) {
			$available_slots = $zone->get_available_slots();
			$name = $zone->name;

			if ( $available_slots === 0 ) {
				$name .= ' (' . __( 'Full', 'real-estate-banner-ads' ) . ')';
			} else {
				$name .= sprintf( ' (%d %s)', $available_slots, __( 'slots available', 'real-estate-banner-ads' ) );
			}

			$options[ $zone->id ] = $name;
		}

		return $options;
	}

	/**
	 * Get zones with availability information for select fields.
	 *
	 * @param bool $active_only Whether to return only active zones.
	 * @return array Array of zone data with availability info.
	 */
	public static function get_options_with_availability( $active_only = true ) {
		$zones = self::get_all( $active_only );
		$options = array();

		foreach ( $zones as $zone ) {
			$available_slots = $zone->get_available_slots();
			$name = $zone->name;

			if ( $available_slots === 0 ) {
				$name .= ' (' . __( 'Full', 'real-estate-banner-ads' ) . ')';
			} else {
				$name .= sprintf( ' (%d %s)', $available_slots, __( 'slots available', 'real-estate-banner-ads' ) );
			}

			$options[ $zone->id ] = array(
				'name' => $name,
				'available_slots' => $available_slots,
				'is_full' => $available_slots === 0,
				'max_banners' => $zone->max_banners,
				'dimensions' => $zone->dimensions
			);
		}

		return $options;
	}

	/**
	 * Create a new zone.
	 *
	 * @param array $data Zone data.
	 * @return Banner_Zone|WP_Error Zone object on success, WP_Error on failure.
	 */
	public static function create( $data ) {
		// Validate required fields
		$required_fields = array( 'id', 'name' );
		foreach ( $required_fields as $field ) {
			if ( empty( $data[ $field ] ) ) {
				return new WP_Error( 'missing_field', sprintf( __( 'Missing required field: %s', 'real-estate-banner-ads' ), $field ) );
			}
		}

		// Validate zone ID format
		if ( ! preg_match( '/^[a-z0-9-_]+$/', $data['id'] ) ) {
			return new WP_Error( 'invalid_zone_id', __( 'Zone ID can only contain lowercase letters, numbers, hyphens, and underscores', 'real-estate-banner-ads' ) );
		}

		// Check if zone already exists
		$existing_zone = new self( $data['id'] );
		if ( $existing_zone->id ) {
			return new WP_Error( 'zone_exists', __( 'A zone with this ID already exists', 'real-estate-banner-ads' ) );
		}

		$zone = new self();
		$zone->id = sanitize_text_field( $data['id'] );
		$zone->name = sanitize_text_field( $data['name'] );
		$zone->description = sanitize_textarea_field( $data['description'] ?? '' );
		$zone->max_banners = max( 1, intval( $data['max_banners'] ?? 1 ) );
		$zone->dimensions = sanitize_text_field( $data['dimensions'] ?? '' );
		$zone->active = ! empty( $data['active'] );

		$result = $zone->save();
		
		if ( ! $result ) {
			return new WP_Error( 'save_failed', __( 'Failed to save zone', 'real-estate-banner-ads' ) );
		}

		return $zone;
	}

	/**
	 * Update an existing zone.
	 *
	 * @param string $zone_id Zone ID.
	 * @param array  $data    Zone data.
	 * @return Banner_Zone|WP_Error Zone object on success, WP_Error on failure.
	 */
	public static function update( $zone_id, $data ) {
		$zone = new self( $zone_id );
		
		if ( ! $zone->id ) {
			return new WP_Error( 'zone_not_found', __( 'Zone not found', 'real-estate-banner-ads' ) );
		}

		// Update fields
		if ( isset( $data['name'] ) ) {
			$zone->name = sanitize_text_field( $data['name'] );
		}
		if ( isset( $data['description'] ) ) {
			$zone->description = sanitize_textarea_field( $data['description'] );
		}
		if ( isset( $data['max_banners'] ) ) {
			$zone->max_banners = max( 1, intval( $data['max_banners'] ) );
		}
		if ( isset( $data['dimensions'] ) ) {
			$zone->dimensions = sanitize_text_field( $data['dimensions'] );
		}
		if ( isset( $data['active'] ) ) {
			$zone->active = ! empty( $data['active'] );
		}

		$result = $zone->save();
		
		if ( ! $result ) {
			return new WP_Error( 'save_failed', __( 'Failed to update zone', 'real-estate-banner-ads' ) );
		}

		return $zone;
	}

	/**
	 * Get a single zone by ID.
	 *
	 * @param string $zone_id Zone ID.
	 * @return Banner_Zone|WP_Error Zone object on success, WP_Error on failure.
	 */
	public static function get_by_id( $zone_id ) {
		$zone = new self( $zone_id );
		
		if ( ! $zone->id ) {
			return new WP_Error( 'zone_not_found', __( 'Zone not found', 'real-estate-banner-ads' ) );
		}

		return $zone;
	}
}
