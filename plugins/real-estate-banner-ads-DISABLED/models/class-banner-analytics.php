<?php
/**
 * Banner Analytics model class
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/models
 */

/**
 * Banner Analytics model.
 *
 * Handles analytics tracking and reporting for banner campaigns.
 */
class Banner_Analytics {

	/**
	 * Track a banner impression.
	 *
	 * @param int    $campaign_id Campaign ID.
	 * @param string $zone_id     Zone ID.
	 * @param int    $user_id     User ID (optional).
	 * @return bool True on success, false on failure.
	 */
	public static function track_impression( $campaign_id, $zone_id = '', $user_id = null ) {
		global $wpdb;

		$table_name = $wpdb->prefix . 'banner_impressions';
		
		// Get user info
		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		// Get visitor info
		$ip_address = self::get_client_ip();
		$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
		$page_url = $_SERVER['HTTP_REFERER'] ?? '';

		// Check for duplicate impression (same IP, campaign, and page within 1 hour)
		$duplicate_check = $wpdb->get_var( $wpdb->prepare(
			"SELECT COUNT(*) FROM $table_name 
			WHERE campaign_id = %d 
			AND ip_address = %s 
			AND page_url = %s 
			AND timestamp > DATE_SUB(NOW(), INTERVAL 1 HOUR)",
			$campaign_id,
			$ip_address,
			$page_url
		));

		if ( $duplicate_check > 0 ) {
			return false; // Skip duplicate impression
		}

		$result = $wpdb->insert(
			$table_name,
			array(
				'campaign_id' => $campaign_id,
				'user_id' => $user_id,
				'ip_address' => $ip_address,
				'user_agent' => $user_agent,
				'page_url' => $page_url,
				'zone_id' => $zone_id,
				'timestamp' => current_time( 'mysql' )
			),
			array( '%d', '%d', '%s', '%s', '%s', '%s', '%s' )
		);

		return $result !== false;
	}

	/**
	 * Track a banner click.
	 *
	 * @param int    $campaign_id Campaign ID.
	 * @param string $zone_id     Zone ID.
	 * @param int    $user_id     User ID (optional).
	 * @return bool True on success, false on failure.
	 */
	public static function track_click( $campaign_id, $zone_id = '', $user_id = null ) {
		global $wpdb;

		$table_name = $wpdb->prefix . 'banner_clicks';
		
		// Get user info
		if ( ! $user_id && is_user_logged_in() ) {
			$user_id = get_current_user_id();
		}

		// Get visitor info
		$ip_address = self::get_client_ip();
		$user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
		$page_url = $_SERVER['HTTP_REFERER'] ?? '';

		$result = $wpdb->insert(
			$table_name,
			array(
				'campaign_id' => $campaign_id,
				'user_id' => $user_id,
				'ip_address' => $ip_address,
				'user_agent' => $user_agent,
				'page_url' => $page_url,
				'zone_id' => $zone_id,
				'timestamp' => current_time( 'mysql' )
			),
			array( '%d', '%d', '%s', '%s', '%s', '%s', '%s' )
		);

		return $result !== false;
	}

	/**
	 * Get impression count for a campaign.
	 *
	 * @param int    $campaign_id Campaign ID.
	 * @param string $date_from   Start date (Y-m-d format).
	 * @param string $date_to     End date (Y-m-d format).
	 * @return int Impression count.
	 */
	public static function get_impressions( $campaign_id, $date_from = '', $date_to = '' ) {
		global $wpdb;

		$table_name = $wpdb->prefix . 'banner_impressions';
		$where_clause = 'WHERE campaign_id = %d';
		$params = array( $campaign_id );

		if ( $date_from ) {
			$where_clause .= ' AND DATE(timestamp) >= %s';
			$params[] = $date_from;
		}

		if ( $date_to ) {
			$where_clause .= ' AND DATE(timestamp) <= %s';
			$params[] = $date_to;
		}

		$sql = "SELECT COUNT(*) FROM $table_name $where_clause";
		
		return intval( $wpdb->get_var( $wpdb->prepare( $sql, $params ) ) );
	}

	/**
	 * Get click count for a campaign.
	 *
	 * @param int    $campaign_id Campaign ID.
	 * @param string $date_from   Start date (Y-m-d format).
	 * @param string $date_to     End date (Y-m-d format).
	 * @return int Click count.
	 */
	public static function get_clicks( $campaign_id, $date_from = '', $date_to = '' ) {
		global $wpdb;

		$table_name = $wpdb->prefix . 'banner_clicks';
		$where_clause = 'WHERE campaign_id = %d';
		$params = array( $campaign_id );

		if ( $date_from ) {
			$where_clause .= ' AND DATE(timestamp) >= %s';
			$params[] = $date_from;
		}

		if ( $date_to ) {
			$where_clause .= ' AND DATE(timestamp) <= %s';
			$params[] = $date_to;
		}

		$sql = "SELECT COUNT(*) FROM $table_name $where_clause";
		
		return intval( $wpdb->get_var( $wpdb->prepare( $sql, $params ) ) );
	}

	/**
	 * Get CTR (Click-Through Rate) for a campaign.
	 *
	 * @param int    $campaign_id Campaign ID.
	 * @param string $date_from   Start date (Y-m-d format).
	 * @param string $date_to     End date (Y-m-d format).
	 * @return float CTR percentage.
	 */
	public static function get_ctr( $campaign_id, $date_from = '', $date_to = '' ) {
		$impressions = self::get_impressions( $campaign_id, $date_from, $date_to );
		$clicks = self::get_clicks( $campaign_id, $date_from, $date_to );

		if ( $impressions === 0 ) {
			return 0;
		}

		return round( ( $clicks / $impressions ) * 100, 2 );
	}

	/**
	 * Get daily analytics for a campaign.
	 *
	 * @param int    $campaign_id Campaign ID.
	 * @param string $date_from   Start date (Y-m-d format).
	 * @param string $date_to     End date (Y-m-d format).
	 * @return array Daily analytics data.
	 */
	public static function get_daily_analytics( $campaign_id, $date_from = '', $date_to = '' ) {
		global $wpdb;

		if ( ! $date_from ) {
			$date_from = date( 'Y-m-d', strtotime( '-30 days' ) );
		}
		if ( ! $date_to ) {
			$date_to = date( 'Y-m-d' );
		}

		// Get daily impressions
		$impressions_table = $wpdb->prefix . 'banner_impressions';
		$impressions_sql = $wpdb->prepare(
			"SELECT DATE(timestamp) as date, COUNT(*) as count 
			FROM $impressions_table 
			WHERE campaign_id = %d 
			AND DATE(timestamp) BETWEEN %s AND %s 
			GROUP BY DATE(timestamp) 
			ORDER BY date",
			$campaign_id,
			$date_from,
			$date_to
		);
		$impressions_data = $wpdb->get_results( $impressions_sql, ARRAY_A );

		// Get daily clicks
		$clicks_table = $wpdb->prefix . 'banner_clicks';
		$clicks_sql = $wpdb->prepare(
			"SELECT DATE(timestamp) as date, COUNT(*) as count 
			FROM $clicks_table 
			WHERE campaign_id = %d 
			AND DATE(timestamp) BETWEEN %s AND %s 
			GROUP BY DATE(timestamp) 
			ORDER BY date",
			$campaign_id,
			$date_from,
			$date_to
		);
		$clicks_data = $wpdb->get_results( $clicks_sql, ARRAY_A );

		// Combine data
		$analytics = array();
		$impressions_by_date = array();
		$clicks_by_date = array();

		foreach ( $impressions_data as $row ) {
			$impressions_by_date[ $row['date'] ] = intval( $row['count'] );
		}

		foreach ( $clicks_data as $row ) {
			$clicks_by_date[ $row['date'] ] = intval( $row['count'] );
		}

		// Generate complete date range
		$current_date = strtotime( $date_from );
		$end_date = strtotime( $date_to );

		while ( $current_date <= $end_date ) {
			$date_str = date( 'Y-m-d', $current_date );
			$impressions = $impressions_by_date[ $date_str ] ?? 0;
			$clicks = $clicks_by_date[ $date_str ] ?? 0;
			$ctr = $impressions > 0 ? round( ( $clicks / $impressions ) * 100, 2 ) : 0;

			$analytics[] = array(
				'date' => $date_str,
				'impressions' => $impressions,
				'clicks' => $clicks,
				'ctr' => $ctr
			);

			$current_date = strtotime( '+1 day', $current_date );
		}

		return $analytics;
	}

	/**
	 * Get analytics summary for multiple campaigns.
	 *
	 * @param array  $campaign_ids Array of campaign IDs.
	 * @param string $date_from    Start date (Y-m-d format).
	 * @param string $date_to      End date (Y-m-d format).
	 * @return array Analytics summary.
	 */
	public static function get_campaigns_summary( $campaign_ids, $date_from = '', $date_to = '' ) {
		if ( empty( $campaign_ids ) ) {
			return array(
				'total_impressions' => 0,
				'total_clicks' => 0,
				'average_ctr' => 0,
				'campaigns' => array()
			);
		}

		$campaigns_data = array();
		$total_impressions = 0;
		$total_clicks = 0;

		foreach ( $campaign_ids as $campaign_id ) {
			$impressions = self::get_impressions( $campaign_id, $date_from, $date_to );
			$clicks = self::get_clicks( $campaign_id, $date_from, $date_to );
			$ctr = self::get_ctr( $campaign_id, $date_from, $date_to );

			$campaigns_data[] = array(
				'campaign_id' => $campaign_id,
				'impressions' => $impressions,
				'clicks' => $clicks,
				'ctr' => $ctr
			);

			$total_impressions += $impressions;
			$total_clicks += $clicks;
		}

		$average_ctr = $total_impressions > 0 ? round( ( $total_clicks / $total_impressions ) * 100, 2 ) : 0;

		return array(
			'total_impressions' => $total_impressions,
			'total_clicks' => $total_clicks,
			'average_ctr' => $average_ctr,
			'campaigns' => $campaigns_data
		);
	}

	/**
	 * Clean up old analytics data.
	 *
	 * @param int $days_to_keep Number of days to keep data for.
	 * @return bool True on success, false on failure.
	 */
	public static function cleanup_old_data( $days_to_keep = 365 ) {
		global $wpdb;

		$cutoff_date = date( 'Y-m-d H:i:s', strtotime( "-$days_to_keep days" ) );

		// Clean impressions
		$impressions_table = $wpdb->prefix . 'banner_impressions';
		$impressions_result = $wpdb->query( $wpdb->prepare(
			"DELETE FROM $impressions_table WHERE timestamp < %s",
			$cutoff_date
		));

		// Clean clicks
		$clicks_table = $wpdb->prefix . 'banner_clicks';
		$clicks_result = $wpdb->query( $wpdb->prepare(
			"DELETE FROM $clicks_table WHERE timestamp < %s",
			$cutoff_date
		));

		return $impressions_result !== false && $clicks_result !== false;
	}

	/**
	 * Get client IP address.
	 *
	 * @return string Client IP address.
	 */
	private static function get_client_ip() {
		$ip_keys = array( 'HTTP_CLIENT_IP', 'HTTP_X_FORWARDED_FOR', 'REMOTE_ADDR' );
		
		foreach ( $ip_keys as $key ) {
			if ( array_key_exists( $key, $_SERVER ) === true ) {
				foreach ( explode( ',', $_SERVER[ $key ] ) as $ip ) {
					$ip = trim( $ip );
					if ( filter_var( $ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE ) !== false ) {
						return $ip;
					}
				}
			}
		}

		return $_SERVER['REMOTE_ADDR'] ?? '0.0.0.0';
	}
}
