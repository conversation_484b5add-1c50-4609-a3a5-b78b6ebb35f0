<?php
/**
 * Banner Campaign model class
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/models
 */

/**
 * Banner Campaign model.
 *
 * Handles banner campaign data and operations.
 */
class Banner_Campaign {

	/**
	 * Campaign ID.
	 *
	 * @var int
	 */
	public $id;

	/**
	 * Campaign title.
	 *
	 * @var string
	 */
	public $title;

	/**
	 * Campaign status.
	 *
	 * @var string
	 */
	public $status;

	/**
	 * Campaign owner user ID.
	 *
	 * @var int
	 */
	public $user_id;

	/**
	 * Banner type (image or html).
	 *
	 * @var string
	 */
	public $banner_type;

	/**
	 * Banner image URL.
	 *
	 * @var string
	 */
	public $image_url;

	/**
	 * Banner HTML content.
	 *
	 * @var string
	 */
	public $html_content;

	/**
	 * Banner link URL.
	 *
	 * @var string
	 */
	public $link_url;

	/**
	 * Banner link target.
	 *
	 * @var string
	 */
	public $link_target;

	/**
	 * Banner alt text.
	 *
	 * @var string
	 */
	public $alt_text;

	/**
	 * Ad zone.
	 *
	 * @var string
	 */
	public $zone;

	/**
	 * Campaign start date.
	 *
	 * @var string
	 */
	public $start_date;

	/**
	 * Campaign end date.
	 *
	 * @var string
	 */
	public $end_date;

	/**
	 * WooCommerce order ID.
	 *
	 * @var int
	 */
	public $order_id;

	/**
	 * Constructor.
	 *
	 * @param int $campaign_id Campaign ID.
	 */
	public function __construct( $campaign_id = 0 ) {
		if ( $campaign_id ) {
			$this->load( $campaign_id );
		}
	}

	/**
	 * Load campaign data.
	 *
	 * @param int $campaign_id Campaign ID.
	 * @return bool True on success, false on failure.
	 */
	public function load( $campaign_id ) {
		$post = get_post( $campaign_id );
		
		if ( ! $post || $post->post_type !== 'banner_campaign' ) {
			return false;
		}

		$this->id = $post->ID;
		$this->title = $post->post_title;
		$this->user_id = $post->post_author;
		$this->status = get_post_meta( $post->ID, '_campaign_status', true );
		$this->banner_type = get_post_meta( $post->ID, '_banner_type', true );
		$this->image_url = get_post_meta( $post->ID, '_banner_image_url', true );
		$this->html_content = get_post_meta( $post->ID, '_banner_html_content', true );
		$this->link_url = get_post_meta( $post->ID, '_banner_link_url', true );
		$this->link_target = get_post_meta( $post->ID, '_banner_link_target', true );
		$this->alt_text = get_post_meta( $post->ID, '_banner_alt_text', true );
		$this->zone = get_post_meta( $post->ID, '_banner_zone', true );
		$this->start_date = get_post_meta( $post->ID, '_campaign_start_date', true );
		$this->end_date = get_post_meta( $post->ID, '_campaign_end_date', true );
		$this->order_id = get_post_meta( $post->ID, '_woocommerce_order_id', true );

		return true;
	}

	/**
	 * Save campaign data.
	 *
	 * @return int|WP_Error Campaign ID on success, WP_Error on failure.
	 */
	public function save() {
		$post_data = array(
			'post_title' => $this->title,
			'post_type' => 'banner_campaign',
			'post_status' => 'publish',
			'post_author' => $this->user_id
		);

		if ( $this->id ) {
			$post_data['ID'] = $this->id;
			$result = wp_update_post( $post_data );
		} else {
			$result = wp_insert_post( $post_data );
		}

		if ( is_wp_error( $result ) ) {
			return $result;
		}

		$this->id = $result;

		// Save meta data
		$meta_fields = array(
			'_campaign_status' => $this->status,
			'_banner_type' => $this->banner_type,
			'_banner_image_url' => $this->image_url,
			'_banner_html_content' => $this->html_content,
			'_banner_link_url' => $this->link_url,
			'_banner_link_target' => $this->link_target,
			'_banner_alt_text' => $this->alt_text,
			'_banner_zone' => $this->zone,
			'_campaign_start_date' => $this->start_date,
			'_campaign_end_date' => $this->end_date,
			'_woocommerce_order_id' => $this->order_id
		);

		foreach ( $meta_fields as $key => $value ) {
			if ( $value !== null ) {
				update_post_meta( $this->id, $key, $value );
			}
		}

		// Update taxonomy
		if ( $this->status ) {
			wp_set_post_terms( $this->id, array( $this->status ), 'campaign_status' );
		}

		return $this->id;
	}

	/**
	 * Delete campaign.
	 *
	 * @return bool True on success, false on failure.
	 */
	public function delete() {
		if ( ! $this->id ) {
			return false;
		}

		$result = wp_delete_post( $this->id, true );
		return $result !== false;
	}

	/**
	 * Check if campaign is active.
	 *
	 * @return bool True if active, false otherwise.
	 */
	public function is_active() {
		if ( $this->status !== 'active' ) {
			return false;
		}

		$now = current_time( 'timestamp' );
		$start = strtotime( $this->start_date );
		$end = strtotime( $this->end_date );

		return $now >= $start && $now <= $end;
	}

	/**
	 * Get campaign analytics.
	 *
	 * @return array Analytics data.
	 */
	public function get_analytics() {
		if ( ! $this->id ) {
			return array(
				'impressions' => 0,
				'clicks' => 0,
				'ctr' => 0
			);
		}

		$impressions = Banner_Analytics::get_impressions( $this->id );
		$clicks = Banner_Analytics::get_clicks( $this->id );
		$ctr = $impressions > 0 ? ( $clicks / $impressions ) * 100 : 0;

		return array(
			'impressions' => $impressions,
			'clicks' => $clicks,
			'ctr' => round( $ctr, 2 )
		);
	}

	/**
	 * Create a new campaign from form data.
	 *
	 * @param array $data Form data.
	 * @return Banner_Campaign|WP_Error Campaign object on success, WP_Error on failure.
	 */
	public static function create_from_form( $data ) {
		$campaign = new self();
		
		// Validate required fields
		$required_fields = array( 'title', 'banner_type', 'zone', 'user_id' );
		foreach ( $required_fields as $field ) {
			if ( empty( $data[ $field ] ) ) {
				return new WP_Error( 'missing_field', sprintf( __( 'Missing required field: %s', 'real-estate-banner-ads' ), $field ) );
			}
		}

		// Validate banner type
		if ( ! in_array( $data['banner_type'], array( 'image', 'html' ) ) ) {
			return new WP_Error( 'invalid_banner_type', __( 'Invalid banner type', 'real-estate-banner-ads' ) );
		}

		// Validate zone
		$zones = get_option( 'banner_ads_zones', array() );
		if ( ! isset( $zones[ $data['zone'] ] ) ) {
			return new WP_Error( 'invalid_zone', __( 'Invalid ad zone', 'real-estate-banner-ads' ) );
		}

		// Set campaign data
		$campaign->title = sanitize_text_field( $data['title'] );
		$campaign->user_id = intval( $data['user_id'] );
		$campaign->banner_type = sanitize_text_field( $data['banner_type'] );
		$campaign->zone = sanitize_text_field( $data['zone'] );
		$campaign->status = 'pending';

		if ( $data['banner_type'] === 'image' ) {
			if ( empty( $data['image_url'] ) ) {
				return new WP_Error( 'missing_image', __( 'Image URL is required for image banners', 'real-estate-banner-ads' ) );
			}
			$campaign->image_url = esc_url_raw( $data['image_url'] );
			$campaign->alt_text = sanitize_text_field( $data['alt_text'] ?? '' );
		} else {
			if ( empty( $data['html_content'] ) ) {
				return new WP_Error( 'missing_html', __( 'HTML content is required for HTML banners', 'real-estate-banner-ads' ) );
			}
			$campaign->html_content = wp_kses_post( $data['html_content'] );
		}

		if ( ! empty( $data['link_url'] ) ) {
			$campaign->link_url = esc_url_raw( $data['link_url'] );
			$campaign->link_target = in_array( $data['link_target'] ?? '', array( '_blank', '_self' ) ) ? $data['link_target'] : '_blank';
		}

		$result = $campaign->save();
		
		if ( is_wp_error( $result ) ) {
			return $result;
		}

		return $campaign;
	}

	/**
	 * Get campaigns by status.
	 *
	 * @param string $status Campaign status.
	 * @param int    $limit  Number of campaigns to retrieve.
	 * @return array Array of Banner_Campaign objects.
	 */
	public static function get_by_status( $status, $limit = -1 ) {
		$args = array(
			'post_type' => 'banner_campaign',
			'post_status' => 'publish',
			'posts_per_page' => $limit,
			'meta_query' => array(
				array(
					'key' => '_campaign_status',
					'value' => $status,
					'compare' => '='
				)
			)
		);

		$posts = get_posts( $args );
		$campaigns = array();

		foreach ( $posts as $post ) {
			$campaign = new self();
			if ( $campaign->load( $post->ID ) ) {
				$campaigns[] = $campaign;
			}
		}

		return $campaigns;
	}
}
