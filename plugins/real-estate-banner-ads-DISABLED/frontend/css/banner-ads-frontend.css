/**
 * Frontend CSS for Banner Ads
 *
 * @package RealEstateBannerAds
 */

/* Banner Zone Styles */
.banner-ads-zone {
    margin: 20px 0;
    text-align: center;
    overflow: hidden;
}

.banner-ads-zone.banner-ads-zone-homepage-header {
    margin: 30px 0;
}

.banner-ads-zone.banner-ads-zone-sidebar {
    margin: 15px 0;
}

.banner-ads-zone.banner-ads-zone-footer {
    margin: 20px 0;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
}

/* Banner Ad Styles */
.banner-ad {
    display: inline-block;
    margin: 10px;
    position: relative;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.banner-ad:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.banner-ad img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    transition: opacity 0.3s ease;
}

.banner-ad:hover img {
    opacity: 0.9;
}

.banner-link {
    display: block;
    text-decoration: none;
    border: none;
    outline: none;
}

.banner-link:focus {
    outline: 2px solid #0073aa;
    outline-offset: 2px;
}

/* HTML Banner Styles */
.banner-html {
    border-radius: 4px;
    overflow: hidden;
}

.banner-html * {
    max-width: 100% !important;
}

/* Responsive Banner Zones */
@media (max-width: 768px) {
    .banner-ads-zone.banner-ads-zone-footer {
        flex-direction: column;
        align-items: center;
    }
    
    .banner-ad {
        margin: 5px;
    }
    
    .banner-ads-zone-homepage-header .banner-ad,
    .banner-ads-zone-category-header .banner-ad {
        max-width: 100%;
    }
}

/* Client Dashboard Styles */
.banner-ads-dashboard {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    margin: 20px 0;
}

.banner-ads-dashboard h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #0073aa;
    padding-bottom: 10px;
}

.banner-campaigns-list {
    margin: 20px 0;
}

.banner-campaign-item {
    background: #f9f9f9;
    border: 1px solid #e5e5e5;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.campaign-info h4 {
    margin: 0 0 5px 0;
    color: #333;
}

.campaign-meta {
    font-size: 14px;
    color: #666;
    margin: 5px 0;
}

.campaign-status {
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.campaign-status.status-pending {
    background: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.campaign-status.status-approved {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.campaign-status.status-active {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.campaign-status.status-expired {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.campaign-status.status-rejected {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.campaign-stats {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 18px;
    font-weight: bold;
    color: #0073aa;
    display: block;
}

.stat-label {
    font-size: 12px;
    color: #666;
    text-transform: uppercase;
}

/* Upload Form Styles */
.banner-upload-form {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 20px;
    margin: 20px 0;
}

.banner-upload-form h3 {
    margin-top: 0;
    color: #333;
}

.form-row {
    margin-bottom: 20px;
}

.form-row label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #333;
}

.form-row input,
.form-row select,
.form-row textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-row textarea {
    min-height: 100px;
    resize: vertical;
}

.form-row.form-row-half {
    width: 48%;
    display: inline-block;
    margin-right: 2%;
}

.form-row.form-row-half:last-child {
    margin-right: 0;
}

.banner-type-toggle {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.banner-type-option {
    flex: 1;
    text-align: center;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.banner-type-option.active {
    border-color: #0073aa;
    background: #f0f8ff;
}

.banner-type-option input[type="radio"] {
    display: none;
}

.conditional-field {
    display: none;
}

.conditional-field.active {
    display: block;
}

.file-upload-area {
    border: 2px dashed #ddd;
    border-radius: 4px;
    padding: 40px 20px;
    text-align: center;
    transition: border-color 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover,
.file-upload-area.dragover {
    border-color: #0073aa;
    background: #f0f8ff;
}

.file-upload-area.has-file {
    border-color: #28a745;
    background: #f8fff9;
}

.upload-icon {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 10px;
}

.upload-text {
    color: #666;
    margin-bottom: 10px;
}

.file-info {
    margin-top: 10px;
    padding: 10px;
    background: #f9f9f9;
    border-radius: 4px;
    display: none;
}

.file-info.show {
    display: block;
}

.btn-primary {
    background: #0073aa;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background 0.3s ease;
}

.btn-primary:hover {
    background: #005a87;
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
    .banner-campaign-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .campaign-stats {
        width: 100%;
        justify-content: space-around;
    }
    
    .form-row.form-row-half {
        width: 100%;
        margin-right: 0;
        margin-bottom: 15px;
    }
    
    .banner-type-toggle {
        flex-direction: column;
    }
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0,115,170,.3);
    border-radius: 50%;
    border-top-color: #0073aa;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    white-space: nowrap;
    border: 0;
}
