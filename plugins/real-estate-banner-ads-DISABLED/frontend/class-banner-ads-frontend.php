<?php
/**
 * The public-facing functionality of the plugin
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/frontend
 */

/**
 * The public-facing functionality of the plugin.
 *
 * Defines the plugin name, version, and hooks for public-facing functionality.
 */
class Banner_Ads_Frontend {

	/**
	 * The ID of this plugin.
	 *
	 * @var string $plugin_name The ID of this plugin.
	 */
	private $plugin_name;

	/**
	 * The version of this plugin.
	 *
	 * @var string $version The current version of this plugin.
	 */
	private $version;

	/**
	 * Initialize the class and set its properties.
	 *
	 * @param string $plugin_name The name of the plugin.
	 * @param string $version     The version of this plugin.
	 */
	public function __construct( $plugin_name, $version ) {
		$this->plugin_name = $plugin_name;
		$this->version = $version;
	}

	/**
	 * Register the stylesheets for the public-facing side of the site.
	 */
	public function enqueue_styles() {
		wp_enqueue_style( 
			$this->plugin_name, 
			REAL_ESTATE_BANNER_ADS_URL . 'frontend/css/banner-ads-frontend.css', 
			array(), 
			$this->version, 
			'all' 
		);
	}

	/**
	 * Register the JavaScript for the public-facing side of the site.
	 */
	public function enqueue_scripts() {
		wp_enqueue_script( 
			$this->plugin_name, 
			REAL_ESTATE_BANNER_ADS_URL . 'frontend/js/banner-ads-frontend.js', 
			array( 'jquery' ), 
			$this->version, 
			false 
		);

		// Localize script for AJAX tracking
		wp_localize_script( $this->plugin_name, 'banner_ads_ajax', array(
			'ajax_url' => admin_url( 'admin-ajax.php' ),
			'nonce' => wp_create_nonce( 'banner_ads_tracking_nonce' )
		));
	}

	/**
	 * Register shortcodes.
	 */
	public function register_shortcodes() {
		add_shortcode( 'banner_ads_zone', array( $this, 'display_banner_zone' ) );
		add_shortcode( 'banner_ads_dashboard', array( $this, 'display_client_dashboard' ) );
		add_shortcode( 'banner_ads_upload', array( $this, 'display_upload_form' ) );
	}

	/**
	 * Display banner zone shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string HTML output.
	 */
	public function display_banner_zone( $atts ) {
		$atts = shortcode_atts( array(
			'zone' => 'homepage-header',
			'limit' => 3,
			'class' => ''
		), $atts, 'banner_ads_zone' );

		$zone_id = sanitize_text_field( $atts['zone'] );
		$limit = intval( $atts['limit'] );
		$class = sanitize_text_field( $atts['class'] );

		// Get active banners for this zone
		$banners = $this->get_active_banners_for_zone( $zone_id, $limit );

		if ( empty( $banners ) ) {
			return '';
		}

		ob_start();
		?>
		<div class="banner-ads-zone banner-ads-zone-<?php echo esc_attr( $zone_id ); ?> <?php echo esc_attr( $class ); ?>">
			<?php foreach ( $banners as $banner ) : ?>
				<div class="banner-ad" data-campaign-id="<?php echo esc_attr( $banner['id'] ); ?>" data-zone="<?php echo esc_attr( $zone_id ); ?>">
					<?php if ( $banner['type'] === 'image' ) : ?>
						<a href="<?php echo esc_url( $banner['link_url'] ); ?>" target="<?php echo esc_attr( $banner['link_target'] ); ?>" class="banner-link">
							<img src="<?php echo esc_url( $banner['image_url'] ); ?>" alt="<?php echo esc_attr( $banner['alt_text'] ); ?>" class="banner-image">
						</a>
					<?php else : ?>
						<div class="banner-html">
							<?php echo wp_kses_post( $banner['html_content'] ); ?>
						</div>
					<?php endif; ?>
				</div>
			<?php endforeach; ?>
		</div>
		<?php
		return ob_get_clean();
	}

	/**
	 * Display client dashboard shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string HTML output.
	 */
	public function display_client_dashboard( $atts ) {
		if ( ! is_user_logged_in() ) {
			return '<p>' . __( 'Please log in to access your banner ads dashboard.', 'real-estate-banner-ads' ) . '</p>';
		}

		$user_id = get_current_user_id();
		$campaigns = $this->get_user_campaigns( $user_id );

		ob_start();
		include REAL_ESTATE_BANNER_ADS_PATH . 'frontend/partials/client-dashboard.php';
		return ob_get_clean();
	}

	/**
	 * Display upload form shortcode.
	 *
	 * @param array $atts Shortcode attributes.
	 * @return string HTML output.
	 */
	public function display_upload_form( $atts ) {
		if ( ! is_user_logged_in() ) {
			return '<p>' . __( 'Please log in to upload banner ads.', 'real-estate-banner-ads' ) . '</p>';
		}

		ob_start();
		include REAL_ESTATE_BANNER_ADS_PATH . 'frontend/partials/upload-form.php';
		return ob_get_clean();
	}

	/**
	 * Get active banners for a specific zone.
	 *
	 * @param string $zone_id Zone identifier.
	 * @param int    $limit   Maximum number of banners to return.
	 * @return array Array of banner data.
	 */
	private function get_active_banners_for_zone( $zone_id, $limit = 3 ) {
		$args = array(
			'post_type' => 'banner_campaign',
			'post_status' => 'publish',
			'posts_per_page' => $limit,
			'meta_query' => array(
				'relation' => 'AND',
				array(
					'key' => '_banner_zone',
					'value' => $zone_id,
					'compare' => '='
				),
				array(
					'key' => '_campaign_status',
					'value' => 'active',
					'compare' => '='
				),
				array(
					'key' => '_campaign_start_date',
					'value' => current_time( 'Y-m-d H:i:s' ),
					'compare' => '<='
				),
				array(
					'key' => '_campaign_end_date',
					'value' => current_time( 'Y-m-d H:i:s' ),
					'compare' => '>='
				)
			),
			'orderby' => 'rand'
		);

		$query = new WP_Query( $args );
		$banners = array();

		if ( $query->have_posts() ) {
			while ( $query->have_posts() ) {
				$query->the_post();
				$post_id = get_the_ID();

				$banner_data = array(
					'id' => $post_id,
					'title' => get_the_title(),
					'type' => get_post_meta( $post_id, '_banner_type', true ),
					'image_url' => get_post_meta( $post_id, '_banner_image_url', true ),
					'html_content' => get_post_meta( $post_id, '_banner_html_content', true ),
					'link_url' => get_post_meta( $post_id, '_banner_link_url', true ),
					'link_target' => get_post_meta( $post_id, '_banner_link_target', true ) ?: '_blank',
					'alt_text' => get_post_meta( $post_id, '_banner_alt_text', true )
				);

				$banners[] = $banner_data;
			}
		}

		wp_reset_postdata();
		return $banners;
	}

	/**
	 * Get campaigns for a specific user.
	 *
	 * @param int $user_id User ID.
	 * @return array Array of campaign data.
	 */
	private function get_user_campaigns( $user_id ) {
		$args = array(
			'post_type' => 'banner_campaign',
			'post_status' => 'any',
			'posts_per_page' => -1,
			'author' => $user_id,
			'orderby' => 'date',
			'order' => 'DESC'
		);

		$query = new WP_Query( $args );
		$campaigns = array();

		if ( $query->have_posts() ) {
			while ( $query->have_posts() ) {
				$query->the_post();
				$post_id = get_the_ID();

				$campaign_data = array(
					'id' => $post_id,
					'title' => get_the_title(),
					'status' => get_post_meta( $post_id, '_campaign_status', true ),
					'zone' => get_post_meta( $post_id, '_banner_zone', true ),
					'start_date' => get_post_meta( $post_id, '_campaign_start_date', true ),
					'end_date' => get_post_meta( $post_id, '_campaign_end_date', true ),
					'impressions' => $this->get_campaign_impressions( $post_id ),
					'clicks' => $this->get_campaign_clicks( $post_id )
				);

				$campaigns[] = $campaign_data;
			}
		}

		wp_reset_postdata();
		return $campaigns;
	}

	/**
	 * Get impression count for a campaign.
	 *
	 * @param int $campaign_id Campaign ID.
	 * @return int Impression count.
	 */
	private function get_campaign_impressions( $campaign_id ) {
		global $wpdb;
		$table_name = $wpdb->prefix . 'banner_impressions';
		
		return $wpdb->get_var( $wpdb->prepare(
			"SELECT COUNT(*) FROM $table_name WHERE campaign_id = %d",
			$campaign_id
		));
	}

	/**
	 * Get click count for a campaign.
	 *
	 * @param int $campaign_id Campaign ID.
	 * @return int Click count.
	 */
	private function get_campaign_clicks( $campaign_id ) {
		global $wpdb;
		$table_name = $wpdb->prefix . 'banner_clicks';
		
		return $wpdb->get_var( $wpdb->prepare(
			"SELECT COUNT(*) FROM $table_name WHERE campaign_id = %d",
			$campaign_id
		));
	}

	/**
	 * Track banner impression via AJAX.
	 */
	public function track_banner_impression() {
		check_ajax_referer( 'banner_ads_tracking_nonce', 'nonce' );

		$campaign_id = intval( $_POST['campaign_id'] );
		$zone_id = sanitize_text_field( $_POST['zone_id'] );

		if ( $campaign_id ) {
			Banner_Analytics::track_impression( $campaign_id, $zone_id );
		}

		wp_die();
	}

	/**
	 * Track banner click via AJAX.
	 */
	public function track_banner_click() {
		check_ajax_referer( 'banner_ads_tracking_nonce', 'nonce' );

		$campaign_id = intval( $_POST['campaign_id'] );
		$zone_id = sanitize_text_field( $_POST['zone_id'] );

		if ( $campaign_id ) {
			Banner_Analytics::track_click( $campaign_id, $zone_id );
		}

		wp_die();
	}
}
