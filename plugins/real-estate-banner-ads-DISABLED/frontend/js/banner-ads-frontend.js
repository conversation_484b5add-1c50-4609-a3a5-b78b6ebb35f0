/**
 * Frontend JavaScript for Banner Ads tracking
 *
 * @package RealEstateBannerAds
 */

(function($) {
    'use strict';

    /**
     * Banner Ads Tracking Object
     */
    var BannerAdsTracking = {
        
        /**
         * Initialize tracking
         */
        init: function() {
            this.trackImpressions();
            this.trackClicks();
        },

        /**
         * Track banner impressions using Intersection Observer
         */
        trackImpressions: function() {
            // Check if Intersection Observer is supported
            if (!('IntersectionObserver' in window)) {
                // Fallback for older browsers
                this.trackImpressionsLegacy();
                return;
            }

            var self = this;
            var observedBanners = new Set();

            // Create intersection observer
            var observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting && !observedBanners.has(entry.target)) {
                        observedBanners.add(entry.target);
                        self.sendImpressionTracking(entry.target);
                    }
                });
            }, {
                threshold: 0.5, // Banner must be 50% visible
                rootMargin: '0px'
            });

            // Observe all banner ads
            $('.banner-ad').each(function() {
                observer.observe(this);
            });
        },

        /**
         * Legacy impression tracking for older browsers
         */
        trackImpressionsLegacy: function() {
            var self = this;
            var trackedBanners = new Set();

            function checkVisibility() {
                $('.banner-ad').each(function() {
                    var $banner = $(this);
                    var bannerId = $banner.data('campaign-id');
                    
                    if (trackedBanners.has(bannerId)) {
                        return;
                    }

                    if (self.isElementInViewport(this)) {
                        trackedBanners.add(bannerId);
                        self.sendImpressionTracking(this);
                    }
                });
            }

            // Check on scroll and resize
            $(window).on('scroll resize', $.throttle(250, checkVisibility));
            
            // Initial check
            checkVisibility();
        },

        /**
         * Check if element is in viewport (legacy method)
         */
        isElementInViewport: function(element) {
            var rect = element.getBoundingClientRect();
            var windowHeight = window.innerHeight || document.documentElement.clientHeight;
            var windowWidth = window.innerWidth || document.documentElement.clientWidth;

            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= windowHeight &&
                rect.right <= windowWidth &&
                rect.height > 0 &&
                rect.width > 0
            );
        },

        /**
         * Track banner clicks
         */
        trackClicks: function() {
            var self = this;

            $(document).on('click', '.banner-ad a, .banner-ad', function(e) {
                var $banner = $(this).closest('.banner-ad');
                
                if ($banner.length) {
                    self.sendClickTracking($banner[0]);
                }
            });
        },

        /**
         * Send impression tracking data
         */
        sendImpressionTracking: function(bannerElement) {
            var $banner = $(bannerElement);
            var campaignId = $banner.data('campaign-id');
            var zoneId = $banner.data('zone');

            if (!campaignId) {
                return;
            }

            this.sendTrackingRequest('track_banner_impression', {
                campaign_id: campaignId,
                zone_id: zoneId
            });
        },

        /**
         * Send click tracking data
         */
        sendClickTracking: function(bannerElement) {
            var $banner = $(bannerElement);
            var campaignId = $banner.data('campaign-id');
            var zoneId = $banner.data('zone');

            if (!campaignId) {
                return;
            }

            this.sendTrackingRequest('track_banner_click', {
                campaign_id: campaignId,
                zone_id: zoneId
            });
        },

        /**
         * Send AJAX tracking request
         */
        sendTrackingRequest: function(action, data) {
            if (typeof banner_ads_ajax === 'undefined') {
                return;
            }

            var requestData = {
                action: action,
                nonce: banner_ads_ajax.nonce
            };

            // Merge additional data
            $.extend(requestData, data);

            $.ajax({
                url: banner_ads_ajax.ajax_url,
                type: 'POST',
                data: requestData,
                success: function(response) {
                    // Tracking successful
                },
                error: function(xhr, status, error) {
                    // Silently fail - don't disrupt user experience
                    console.log('Banner tracking error:', error);
                }
            });
        }
    };

    /**
     * jQuery throttle function
     */
    $.throttle = function(delay, fn) {
        var timer = null;
        return function() {
            var context = this, args = arguments;
            clearTimeout(timer);
            timer = setTimeout(function() {
                fn.apply(context, args);
            }, delay);
        };
    };

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        BannerAdsTracking.init();
    });

    /**
     * Banner Zone Display Handler
     */
    var BannerZoneDisplay = {
        
        /**
         * Initialize banner zone display
         */
        init: function() {
            this.rotateBanners();
            this.handleResponsive();
        },

        /**
         * Rotate banners in zones with multiple ads
         */
        rotateBanners: function() {
            $('.banner-ads-zone').each(function() {
                var $zone = $(this);
                var $banners = $zone.find('.banner-ad');
                
                if ($banners.length > 1) {
                    var currentIndex = 0;
                    
                    // Hide all except first
                    $banners.not(':first').hide();
                    
                    // Rotate every 5 seconds instead of 10
                    setInterval(function() {
                        $banners.eq(currentIndex).fadeOut(300, function() {
                            currentIndex = (currentIndex + 1) % $banners.length;
                            $banners.eq(currentIndex).fadeIn(300);
                        });
                    }, 5000); // Changed from 10000 to 5000 ms
                }
            });
        },

        /**
         * Handle responsive banner display
         */
        handleResponsive: function() {
            $(window).on('resize', function() {
                $('.banner-ad img').each(function() {
                    var $img = $(this);
                    var $container = $img.closest('.banner-ads-zone');
                    
                    // Ensure banners don't overflow their containers
                    if ($img.width() > $container.width()) {
                        $img.css({
                            'max-width': '100%',
                            'height': 'auto'
                        });
                    }
                });
            }).trigger('resize');
        }
    };

    /**
     * Initialize banner zone display
     */
    $(document).ready(function() {
        BannerZoneDisplay.init();
    });

})(jQuery);
