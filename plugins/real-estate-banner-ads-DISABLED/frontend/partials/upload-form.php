<?php
/**
 * Banner Upload Form Template
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/frontend/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$user_id = get_current_user_id();
$zones = Banner_Zone::get_options_with_availability( true );

// Handle form submission
if ( $_POST && isset( $_POST['submit_banner'] ) && wp_verify_nonce( $_POST['banner_upload_nonce'], 'banner_upload_action' ) ) {
	$form_data = array(
		'title' => sanitize_text_field( $_POST['campaign_title'] ),
		'banner_type' => sanitize_text_field( $_POST['banner_type'] ),
		'zone' => sanitize_text_field( $_POST['banner_zone'] ),
		'user_id' => $user_id,
		'link_url' => esc_url_raw( $_POST['link_url'] ),
		'link_target' => sanitize_text_field( $_POST['link_target'] )
	);

	if ( $form_data['banner_type'] === 'image' ) {
		// Handle image upload
		if ( isset( $_FILES['banner_image'] ) && $_FILES['banner_image']['error'] === UPLOAD_ERR_OK ) {
			$upload_result = wp_handle_upload( $_FILES['banner_image'], array( 'test_form' => false ) );
			
			if ( ! isset( $upload_result['error'] ) ) {
				$form_data['image_url'] = $upload_result['url'];
				$form_data['alt_text'] = sanitize_text_field( $_POST['alt_text'] );
			} else {
				$upload_error = $upload_result['error'];
			}
		} else {
			$upload_error = __( 'Please select an image file to upload.', 'real-estate-banner-ads' );
		}
	} else {
		$form_data['html_content'] = wp_kses_post( $_POST['html_content'] );
	}

	if ( ! isset( $upload_error ) ) {
		$campaign = Banner_Campaign::create_from_form( $form_data );

		if ( ! is_wp_error( $campaign ) ) {
			// If admin, automatically activate the campaign
			if ( current_user_can( 'manage_options' ) ) {
				$campaign->status = 'active';
				$campaign->start_date = current_time( 'Y-m-d H:i:s' );
				$campaign->end_date = date( 'Y-m-d H:i:s', strtotime( '+30 days' ) ); // Default 30 days for admin
				$campaign->save();

				$success_message = __( 'Your banner campaign has been created and activated successfully! As an admin, your campaign is live immediately.', 'real-estate-banner-ads' );
			} else {
				$success_message = __( 'Your banner campaign has been submitted successfully! It will be reviewed and activated once you complete your purchase.', 'real-estate-banner-ads' );
			}
		} else {
			$error_message = $campaign->get_error_message();
		}
	} else {
		$error_message = $upload_error;
	}
}
?>

<div class="banner-upload-form" id="upload-form">
	<h3><?php esc_html_e( 'Upload Your Banner Ad', 'real-estate-banner-ads' ); ?></h3>

	<?php if ( current_user_can( 'manage_options' ) ) : ?>
		<div class="banner-notice notice-info">
			<p><strong><?php esc_html_e( 'Admin Mode:', 'real-estate-banner-ads' ); ?></strong> <?php esc_html_e( 'As an administrator, your campaigns will be automatically activated without requiring payment. You can also create campaigns directly from the admin panel.', 'real-estate-banner-ads' ); ?></p>
		</div>
	<?php endif; ?>

	<?php if ( isset( $success_message ) ) : ?>
		<div class="banner-notice notice-success">
			<p><?php echo esc_html( $success_message ); ?></p>
		</div>
	<?php endif; ?>

	<?php if ( isset( $error_message ) ) : ?>
		<div class="banner-notice notice-error">
			<p><?php echo esc_html( $error_message ); ?></p>
		</div>
	<?php endif; ?>

	<form method="post" enctype="multipart/form-data" id="banner-upload-form">
		<?php wp_nonce_field( 'banner_upload_action', 'banner_upload_nonce' ); ?>
		
		<!-- Campaign Title -->
		<div class="form-row">
			<label for="campaign_title"><?php esc_html_e( 'Campaign Title', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label>
			<input type="text" id="campaign_title" name="campaign_title" required value="<?php echo esc_attr( $_POST['campaign_title'] ?? '' ); ?>">
		</div>

		<!-- Banner Type Selection -->
		<div class="form-row">
			<label><?php esc_html_e( 'Banner Type', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label>
			<div class="banner-type-toggle">
				<div class="banner-type-option <?php echo ( ! isset( $_POST['banner_type'] ) || $_POST['banner_type'] === 'image' ) ? 'active' : ''; ?>">
					<input type="radio" id="type_image" name="banner_type" value="image" <?php checked( $_POST['banner_type'] ?? 'image', 'image' ); ?>>
					<label for="type_image">
						<strong><?php esc_html_e( 'Image Banner', 'real-estate-banner-ads' ); ?></strong><br>
						<small><?php esc_html_e( 'Upload JPG, PNG, or GIF image', 'real-estate-banner-ads' ); ?></small>
					</label>
				</div>
				<div class="banner-type-option <?php echo ( isset( $_POST['banner_type'] ) && $_POST['banner_type'] === 'html' ) ? 'active' : ''; ?>">
					<input type="radio" id="type_html" name="banner_type" value="html" <?php checked( $_POST['banner_type'] ?? '', 'html' ); ?>>
					<label for="type_html">
						<strong><?php esc_html_e( 'HTML Banner', 'real-estate-banner-ads' ); ?></strong><br>
						<small><?php esc_html_e( 'Custom HTML/CSS code', 'real-estate-banner-ads' ); ?></small>
					</label>
				</div>
			</div>
		</div>

		<!-- Image Upload Fields -->
		<div class="conditional-field image-fields <?php echo ( ! isset( $_POST['banner_type'] ) || $_POST['banner_type'] === 'image' ) ? 'active' : ''; ?>">
			<div class="form-row">
				<label for="banner_image"><?php esc_html_e( 'Banner Image', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label>
				<div class="file-upload-area" id="file-upload-area">
					<div class="upload-icon">📁</div>
					<div class="upload-text">
						<strong><?php esc_html_e( 'Click to upload', 'real-estate-banner-ads' ); ?></strong> <?php esc_html_e( 'or drag and drop', 'real-estate-banner-ads' ); ?>
					</div>
					<div class="upload-requirements">
						<small><?php esc_html_e( 'Maximum file size: 2MB. Supported formats: JPG, PNG, GIF', 'real-estate-banner-ads' ); ?></small>
					</div>
					<input type="file" id="banner_image" name="banner_image" accept="image/*" style="display: none;">
				</div>
				<div class="file-info" id="file-info">
					<div class="file-name"></div>
					<div class="file-size"></div>
				</div>
			</div>

			<div class="form-row">
				<label for="alt_text"><?php esc_html_e( 'Alt Text', 'real-estate-banner-ads' ); ?></label>
				<input type="text" id="alt_text" name="alt_text" placeholder="<?php esc_attr_e( 'Describe your image for accessibility', 'real-estate-banner-ads' ); ?>" value="<?php echo esc_attr( $_POST['alt_text'] ?? '' ); ?>">
			</div>
		</div>

		<!-- HTML Content Fields -->
		<div class="conditional-field html-fields <?php echo ( isset( $_POST['banner_type'] ) && $_POST['banner_type'] === 'html' ) ? 'active' : ''; ?>">
			<div class="form-row">
				<label for="html_content"><?php esc_html_e( 'HTML Content', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label>
				<textarea id="html_content" name="html_content" placeholder="<?php esc_attr_e( 'Enter your HTML/CSS code here...', 'real-estate-banner-ads' ); ?>"><?php echo esc_textarea( $_POST['html_content'] ?? '' ); ?></textarea>
				<small><?php esc_html_e( 'You can include HTML and CSS. JavaScript is not allowed for security reasons.', 'real-estate-banner-ads' ); ?></small>
			</div>
		</div>

		<!-- Link URL -->
		<div class="form-row">
			<label for="link_url"><?php esc_html_e( 'Link URL', 'real-estate-banner-ads' ); ?></label>
			<input type="url" id="link_url" name="link_url" placeholder="https://example.com" value="<?php echo esc_attr( $_POST['link_url'] ?? '' ); ?>">
		</div>

		<!-- Link Target -->
		<div class="form-row form-row-half">
			<label for="link_target"><?php esc_html_e( 'Link Target', 'real-estate-banner-ads' ); ?></label>
			<select id="link_target" name="link_target">
				<option value="_blank" <?php selected( $_POST['link_target'] ?? '_blank', '_blank' ); ?>><?php esc_html_e( 'New Window', 'real-estate-banner-ads' ); ?></option>
				<option value="_self" <?php selected( $_POST['link_target'] ?? '', '_self' ); ?>><?php esc_html_e( 'Same Window', 'real-estate-banner-ads' ); ?></option>
			</select>
		</div>

		<!-- Ad Zone -->
		<div class="form-row form-row-half">
			<label for="banner_zone"><?php esc_html_e( 'Ad Zone', 'real-estate-banner-ads' ); ?> <span class="required">*</span></label>
			<select id="banner_zone" name="banner_zone" required>
				<option value=""><?php esc_html_e( 'Select a zone...', 'real-estate-banner-ads' ); ?></option>
				<?php foreach ( $zones as $zone_id => $zone_data ) : ?>
					<option value="<?php echo esc_attr( $zone_id ); ?>"
							<?php echo $zone_data['is_full'] ? 'disabled' : ''; ?>
							<?php selected( $_POST['banner_zone'] ?? '', $zone_id ); ?>
							data-available-slots="<?php echo esc_attr( $zone_data['available_slots'] ); ?>"
							data-max-banners="<?php echo esc_attr( $zone_data['max_banners'] ); ?>"
							data-dimensions="<?php echo esc_attr( $zone_data['dimensions'] ); ?>">
						<?php echo esc_html( $zone_data['name'] ); ?>
					</option>
				<?php endforeach; ?>
			</select>
			<small class="zone-help-text" style="display: none; margin-top: 5px; color: #666;"></small>
		</div>

		<!-- Submit Button -->
		<div class="form-row">
			<button type="submit" name="submit_banner" class="btn-primary">
				<?php esc_html_e( 'Submit Banner Campaign', 'real-estate-banner-ads' ); ?>
			</button>
		</div>
	</form>

	<!-- Next Steps -->
	<div class="next-steps">
		<h4><?php esc_html_e( 'Next Steps', 'real-estate-banner-ads' ); ?></h4>
		<ol>
			<li><?php esc_html_e( 'Submit your banner campaign using the form above', 'real-estate-banner-ads' ); ?></li>
			<li><?php esc_html_e( 'Purchase an ad package from our shop', 'real-estate-banner-ads' ); ?></li>
			<li><?php esc_html_e( 'Your campaign will be reviewed and activated automatically', 'real-estate-banner-ads' ); ?></li>
			<li><?php esc_html_e( 'Track your campaign performance in your dashboard', 'real-estate-banner-ads' ); ?></li>
		</ol>
		<p>
			<a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="btn-primary">
				<?php esc_html_e( 'Browse Ad Packages', 'real-estate-banner-ads' ); ?>
			</a>
		</p>
	</div>
</div>

<script>
jQuery(document).ready(function($) {
	// Banner type toggle
	$('.banner-type-option').on('click', function() {
		$('.banner-type-option').removeClass('active');
		$(this).addClass('active');
		$(this).find('input[type="radio"]').prop('checked', true);
		
		var selectedType = $(this).find('input[type="radio"]').val();
		$('.conditional-field').removeClass('active');
		$('.' + selectedType + '-fields').addClass('active');
	});

	// File upload handling
	$('#file-upload-area').on('click', function() {
		$('#banner_image').click();
	});

	$('#banner_image').on('change', function() {
		var file = this.files[0];
		if (file) {
			var fileSize = (file.size / 1024 / 1024).toFixed(2);
			var maxSize = 2; // 2MB
			
			if (fileSize > maxSize) {
				alert('File size must be less than ' + maxSize + 'MB');
				$(this).val('');
				return;
			}
			
			$('#file-upload-area').addClass('has-file');
			$('#file-info').addClass('show');
			$('#file-info .file-name').text(file.name);
			$('#file-info .file-size').text(fileSize + ' MB');
		}
	});

	// Drag and drop
	$('#file-upload-area').on('dragover', function(e) {
		e.preventDefault();
		$(this).addClass('dragover');
	});

	$('#file-upload-area').on('dragleave', function(e) {
		e.preventDefault();
		$(this).removeClass('dragover');
	});

	$('#file-upload-area').on('drop', function(e) {
		e.preventDefault();
		$(this).removeClass('dragover');
		
		var files = e.originalEvent.dataTransfer.files;
		if (files.length > 0) {
			$('#banner_image')[0].files = files;
			$('#banner_image').trigger('change');
		}
	});

	// Form validation
	$('#banner-upload-form').on('submit', function(e) {
		var bannerType = $('input[name="banner_type"]:checked').val();
		var isValid = true;
		var errorMessage = '';

		if (bannerType === 'image') {
			var fileInput = $('#banner_image')[0];
			if (!fileInput.files || fileInput.files.length === 0) {
				isValid = false;
				errorMessage = 'Please select an image file.';
			}
		} else if (bannerType === 'html') {
			var htmlContent = $('#html_content').val().trim();
			if (!htmlContent) {
				isValid = false;
				errorMessage = 'Please enter HTML content.';
			}
		}

		if (!isValid) {
			e.preventDefault();
			alert(errorMessage);
		}
	});
});
</script>

<style>
.required {
	color: #dc3545;
}

.next-steps {
	margin-top: 30px;
	padding: 20px;
	background: #f8f9fa;
	border-radius: 6px;
	border-left: 4px solid #0073aa;
}

.next-steps h4 {
	margin-top: 0;
	color: #333;
}

.next-steps ol {
	margin: 15px 0;
	padding-left: 20px;
}

.next-steps li {
	margin-bottom: 8px;
	color: #666;
}

.banner-notice {
	padding: 12px 15px;
	margin: 15px 0;
	border-radius: 4px;
	border-left: 4px solid;
}

.notice-success {
	background: #d4edda;
	border-color: #28a745;
	color: #155724;
}

.notice-error {
	background: #f8d7da;
	border-color: #dc3545;
	color: #721c24;
}

.notice-info {
	background: #d1ecf1;
	border-color: #17a2b8;
	color: #0c5460;
}
</style>
