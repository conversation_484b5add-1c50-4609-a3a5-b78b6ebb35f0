<?php
/**
 * Client Dashboard Template
 *
 * @package RealEstateBannerAds
 * @subpackage RealEstateBannerAds/frontend/partials
 */

// If this file is called directly, abort.
if ( ! defined( 'WPINC' ) ) {
	die;
}

$user_id = get_current_user_id();
$user = wp_get_current_user();

// Get user's campaigns
$user_campaigns = get_posts( array(
	'post_type' => 'banner_campaign',
	'author' => $user_id,
	'posts_per_page' => -1,
	'post_status' => 'publish',
	'orderby' => 'date',
	'order' => 'DESC'
));

// Calculate summary stats
$total_campaigns = count( $user_campaigns );
$active_campaigns = 0;
$total_impressions = 0;
$total_clicks = 0;

foreach ( $user_campaigns as $campaign_post ) {
	$status = get_post_meta( $campaign_post->ID, '_campaign_status', true );
	if ( $status === 'active' ) {
		$active_campaigns++;
	}
	
	$impressions = Banner_Analytics::get_impressions( $campaign_post->ID );
	$clicks = Banner_Analytics::get_clicks( $campaign_post->ID );
	
	$total_impressions += $impressions;
	$total_clicks += $clicks;
}

$average_ctr = $total_impressions > 0 ? round( ( $total_clicks / $total_impressions ) * 100, 2 ) : 0;
?>

<div class="banner-ads-dashboard">
	<h3><?php printf( esc_html__( 'Welcome, %s!', 'real-estate-banner-ads' ), esc_html( $user->display_name ) ); ?></h3>
	
	<!-- Summary Stats -->
	<div class="dashboard-summary">
		<div class="campaign-stats">
			<div class="stat-item">
				<span class="stat-number"><?php echo esc_html( $total_campaigns ); ?></span>
				<span class="stat-label"><?php esc_html_e( 'Total Campaigns', 'real-estate-banner-ads' ); ?></span>
			</div>
			<div class="stat-item">
				<span class="stat-number"><?php echo esc_html( $active_campaigns ); ?></span>
				<span class="stat-label"><?php esc_html_e( 'Active Campaigns', 'real-estate-banner-ads' ); ?></span>
			</div>
			<div class="stat-item">
				<span class="stat-number"><?php echo esc_html( number_format( $total_impressions ) ); ?></span>
				<span class="stat-label"><?php esc_html_e( 'Total Views', 'real-estate-banner-ads' ); ?></span>
			</div>
			<div class="stat-item">
				<span class="stat-number"><?php echo esc_html( number_format( $total_clicks ) ); ?></span>
				<span class="stat-label"><?php esc_html_e( 'Total Clicks', 'real-estate-banner-ads' ); ?></span>
			</div>
			<div class="stat-item">
				<span class="stat-number"><?php echo esc_html( $average_ctr ); ?>%</span>
				<span class="stat-label"><?php esc_html_e( 'Average CTR', 'real-estate-banner-ads' ); ?></span>
			</div>
		</div>
	</div>

	<!-- Quick Actions -->
	<div class="dashboard-actions">
		<h4><?php esc_html_e( 'Quick Actions', 'real-estate-banner-ads' ); ?></h4>
		<div class="action-buttons">
			<?php if ( current_user_can( 'manage_options' ) ) : ?>
				<a href="<?php echo esc_url( admin_url( 'post-new.php?post_type=banner_campaign' ) ); ?>" class="btn-primary"><?php esc_html_e( 'Create Campaign (Admin)', 'real-estate-banner-ads' ); ?></a>
				<a href="<?php echo esc_url( admin_url( 'admin.php?page=banner-ads' ) ); ?>" class="btn-primary"><?php esc_html_e( 'Admin Dashboard', 'real-estate-banner-ads' ); ?></a>
				<a href="#upload-form" class="btn-primary"><?php esc_html_e( 'Quick Upload', 'real-estate-banner-ads' ); ?></a>
			<?php else : ?>
				<a href="#upload-form" class="btn-primary"><?php esc_html_e( 'Upload New Banner', 'real-estate-banner-ads' ); ?></a>
				<a href="<?php echo esc_url( wc_get_page_permalink( 'shop' ) ); ?>" class="btn-primary"><?php esc_html_e( 'Buy Ad Package', 'real-estate-banner-ads' ); ?></a>
			<?php endif; ?>
		</div>
	</div>

	<!-- Campaigns List -->
	<div class="banner-campaigns-list">
		<h4><?php esc_html_e( 'Your Banner Campaigns', 'real-estate-banner-ads' ); ?></h4>
		
		<?php if ( ! empty( $user_campaigns ) ) : ?>
			<?php foreach ( $user_campaigns as $campaign_post ) : ?>
				<?php
				$campaign_id = $campaign_post->ID;
				$status = get_post_meta( $campaign_id, '_campaign_status', true );
				$zone = get_post_meta( $campaign_id, '_banner_zone', true );
				$start_date = get_post_meta( $campaign_id, '_campaign_start_date', true );
				$end_date = get_post_meta( $campaign_id, '_campaign_end_date', true );
				$banner_type = get_post_meta( $campaign_id, '_banner_type', true );
				$image_url = get_post_meta( $campaign_id, '_banner_image_url', true );
				
				$impressions = Banner_Analytics::get_impressions( $campaign_id );
				$clicks = Banner_Analytics::get_clicks( $campaign_id );
				$ctr = $impressions > 0 ? round( ( $clicks / $impressions ) * 100, 2 ) : 0;
				
				$zones = get_option( 'banner_ads_zones', array() );
				$zone_name = $zones[ $zone ]['name'] ?? $zone;
				?>
				<div class="banner-campaign-item">
					<div class="campaign-info">
						<h4><?php echo esc_html( $campaign_post->post_title ); ?></h4>
						<div class="campaign-meta">
							<span class="campaign-status status-<?php echo esc_attr( $status ); ?>">
								<?php echo esc_html( ucfirst( $status ) ); ?>
							</span>
							<span class="campaign-zone"><?php echo esc_html( $zone_name ); ?></span>
						</div>
						
						<?php if ( $banner_type === 'image' && $image_url ) : ?>
							<div class="campaign-preview">
								<img src="<?php echo esc_url( $image_url ); ?>" alt="<?php echo esc_attr( $campaign_post->post_title ); ?>" style="max-width: 200px; max-height: 100px;">
							</div>
						<?php endif; ?>
						
						<?php if ( $start_date && $end_date ) : ?>
							<div class="campaign-dates">
								<strong><?php esc_html_e( 'Campaign Period:', 'real-estate-banner-ads' ); ?></strong>
								<?php echo esc_html( date( 'M j, Y', strtotime( $start_date ) ) ); ?> - 
								<?php echo esc_html( date( 'M j, Y', strtotime( $end_date ) ) ); ?>
							</div>
						<?php endif; ?>
					</div>
					
					<div class="campaign-performance">
						<div class="campaign-stats">
							<div class="stat-item">
								<span class="stat-number"><?php echo esc_html( number_format( $impressions ) ); ?></span>
								<span class="stat-label"><?php esc_html_e( 'Views', 'real-estate-banner-ads' ); ?></span>
							</div>
							<div class="stat-item">
								<span class="stat-number"><?php echo esc_html( number_format( $clicks ) ); ?></span>
								<span class="stat-label"><?php esc_html_e( 'Clicks', 'real-estate-banner-ads' ); ?></span>
							</div>
							<div class="stat-item">
								<span class="stat-number"><?php echo esc_html( $ctr ); ?>%</span>
								<span class="stat-label"><?php esc_html_e( 'CTR', 'real-estate-banner-ads' ); ?></span>
							</div>
						</div>
					</div>
				</div>
			<?php endforeach; ?>
		<?php else : ?>
			<div class="no-campaigns">
				<p><?php esc_html_e( 'You haven\'t created any banner campaigns yet.', 'real-estate-banner-ads' ); ?></p>
				<p><?php esc_html_e( 'Get started by purchasing an ad package and uploading your first banner!', 'real-estate-banner-ads' ); ?></p>
			</div>
		<?php endif; ?>
	</div>

	<!-- Recent Orders -->
	<?php if ( class_exists( 'WooCommerce' ) ) : ?>
		<?php
		$customer_orders = wc_get_orders( array(
			'customer' => $user_id,
			'limit' => 5,
			'orderby' => 'date',
			'order' => 'DESC',
			'meta_query' => array(
				array(
					'key' => '_contains_banner_products',
					'value' => 'yes',
					'compare' => '='
				)
			)
		));
		?>
		
		<?php if ( ! empty( $customer_orders ) ) : ?>
			<div class="recent-orders">
				<h4><?php esc_html_e( 'Recent Banner Ad Orders', 'real-estate-banner-ads' ); ?></h4>
				<div class="orders-list">
					<?php foreach ( $customer_orders as $order ) : ?>
						<div class="order-item">
							<div class="order-info">
								<strong><?php printf( esc_html__( 'Order #%s', 'real-estate-banner-ads' ), $order->get_order_number() ); ?></strong>
								<span class="order-date"><?php echo esc_html( $order->get_date_created()->date( 'M j, Y' ) ); ?></span>
							</div>
							<div class="order-status">
								<span class="status-badge status-<?php echo esc_attr( $order->get_status() ); ?>">
									<?php echo esc_html( wc_get_order_status_name( $order->get_status() ) ); ?>
								</span>
							</div>
							<div class="order-total">
								<?php echo wp_kses_post( $order->get_formatted_order_total() ); ?>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			</div>
		<?php endif; ?>
	<?php endif; ?>

	<!-- Help Section -->
	<div class="dashboard-help">
		<h4><?php esc_html_e( 'Need Help?', 'real-estate-banner-ads' ); ?></h4>
		<div class="help-links">
			<ul>
				<li><a href="#faq"><?php esc_html_e( 'Frequently Asked Questions', 'real-estate-banner-ads' ); ?></a></li>
				<li><a href="#guidelines"><?php esc_html_e( 'Banner Guidelines', 'real-estate-banner-ads' ); ?></a></li>
				<li><a href="#contact"><?php esc_html_e( 'Contact Support', 'real-estate-banner-ads' ); ?></a></li>
			</ul>
		</div>
	</div>
</div>

<style>
.dashboard-summary {
	background: #f8f9fa;
	padding: 20px;
	border-radius: 6px;
	margin-bottom: 30px;
}

.dashboard-actions {
	margin-bottom: 30px;
}

.action-buttons {
	display: flex;
	gap: 15px;
	margin-top: 10px;
}

.action-buttons .btn-primary {
	text-decoration: none;
	display: inline-block;
}

.no-campaigns {
	text-align: center;
	padding: 40px 20px;
	background: #f8f9fa;
	border-radius: 6px;
	color: #666;
}

.campaign-preview {
	margin: 10px 0;
}

.campaign-dates {
	margin-top: 10px;
	font-size: 14px;
	color: #666;
}

.recent-orders {
	margin-top: 30px;
	padding-top: 30px;
	border-top: 1px solid #eee;
}

.order-item {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 15px;
	background: #f8f9fa;
	border-radius: 4px;
	margin-bottom: 10px;
}

.order-date {
	color: #666;
	font-size: 14px;
	margin-left: 10px;
}

.status-badge {
	padding: 4px 8px;
	border-radius: 3px;
	font-size: 12px;
	font-weight: bold;
	text-transform: uppercase;
}

.status-badge.status-completed {
	background: #d4edda;
	color: #155724;
}

.status-badge.status-processing {
	background: #d1ecf1;
	color: #0c5460;
}

.status-badge.status-pending {
	background: #fff3cd;
	color: #856404;
}

.dashboard-help {
	margin-top: 30px;
	padding-top: 30px;
	border-top: 1px solid #eee;
}

.help-links ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.help-links li {
	margin-bottom: 8px;
}

.help-links a {
	color: #0073aa;
	text-decoration: none;
}

.help-links a:hover {
	text-decoration: underline;
}

@media (max-width: 768px) {
	.action-buttons {
		flex-direction: column;
	}
	
	.order-item {
		flex-direction: column;
		align-items: flex-start;
		gap: 10px;
	}
}
</style>
