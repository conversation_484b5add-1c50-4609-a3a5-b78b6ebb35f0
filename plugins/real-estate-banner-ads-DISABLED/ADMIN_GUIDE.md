# Admin Guide - Banner Ads Manager

## 🔑 **Admin Privileges & Capabilities**

As an administrator, you have **full control** over the banner ads system without any payment restrictions:

### **✅ What Admins Can Do:**

1. **Create Campaigns Directly**
   - Go to **Banner Ads > Banner Campaigns > Add New**
   - Full campaign editor with media uploader
   - Set any status (pending, active, expired, etc.)
   - Set custom start/end dates
   - No payment required

2. **Upload Banners Instantly**
   - Use the frontend upload form
   - **Campaigns auto-activate** for admins
   - Default 30-day duration (customizable)
   - Bypass approval workflow

3. **Manage All Campaigns**
   - View, edit, delete any campaign
   - Change status instantly
   - Override dates and settings
   - View detailed analytics

4. **Zone Management**
   - Create/edit/delete ad zones
   - Set capacity limits
   - Configure dimensions
   - Enable/disable zones

## 🚀 **Quick Start for Admins**

### **Method 1: Admin Panel (Recommended)**
1. Go to **Banner Ads > Banner Campaigns**
2. Click **"Add New"**
3. Fill in campaign details:
   - Title: "My Admin Banner"
   - Banner Type: Image or HTML
   - Upload image or enter HTML
   - Select zone
   - Set link URL
4. In the **Campaign Status** box:
   - Status: Active
   - Click **"Activate Now"** or **"Set 7 Days"**
5. **Publish** the campaign
6. ✅ **Banner is live immediately!**

### **Method 2: Frontend Upload (Quick)**
1. Go to your upload form page
2. You'll see **"Admin Mode"** notice
3. Upload banner normally
4. ✅ **Auto-activated without payment!**

### **Method 3: Dashboard**
1. Go to your dashboard page
2. Click **"Create Campaign (Admin)"**
3. Takes you to admin panel editor

## 🎯 **Admin Campaign Editor Features**

### **Campaign Details Tab:**
- **Banner Type**: Image or HTML
- **Ad Zone**: Select from available zones
- **Image Upload**: WordPress media library integration
- **HTML Editor**: For custom HTML/CSS banners
- **Link Settings**: URL and target window

### **Status & Scheduling Tab:**
- **Status Dropdown**: Set any status directly
- **Date Pickers**: Custom start/end dates
- **Quick Actions**:
  - "Activate Now" - Sets active + current time
  - "Set 7 Days" - Active for 1 week
  - "Set 30 Days" - Active for 1 month

### **Analytics Tab:**
- Real-time performance metrics
- Link to detailed analytics
- Impression/click tracking

## 🔧 **Admin Workflow Examples**

### **Create a Homepage Banner:**
```
1. Banner Ads > Banner Campaigns > Add New
2. Title: "Featured Property Banner"
3. Type: Image
4. Upload: property-banner.jpg
5. Zone: Homepage Header
6. Link: https://yoursite.com/featured-property
7. Status: Active
8. Click "Set 30 Days"
9. Publish
```

### **Create HTML Banner:**
```
1. Banner Ads > Banner Campaigns > Add New
2. Title: "Custom HTML Ad"
3. Type: HTML
4. HTML Content:
   <div style="background: #0073aa; color: white; padding: 20px; text-align: center;">
     <h3>Special Offer!</h3>
     <p>Contact us today for exclusive deals</p>
   </div>
5. Zone: Sidebar
6. Status: Active
7. Publish
```

### **Manage Existing Campaign:**
```
1. Banner Ads > All Campaigns
2. Click campaign title to edit
3. Change status, dates, content
4. Update campaign
```

## 📊 **Admin Analytics Access**

### **View All Analytics:**
- **Banner Ads > Analytics**
- Filter by date range
- Filter by specific campaign
- Export data (if needed)

### **Campaign-Specific Analytics:**
- Edit any campaign
- Check "Campaign Analytics" box
- Click "View Detailed Analytics"

## ⚙️ **Admin Settings**

### **Configure Plugin:**
- **Banner Ads > Settings**
- Auto-approval settings
- File upload limits
- Email notifications
- Analytics retention

### **Manage Zones:**
- **Banner Ads > Ad Zones**
- Create custom zones
- Set capacity limits
- Configure dimensions

## 🎨 **Zone Integration**

### **Add Zones to Your Site:**
```php
// Homepage header
[banner_ads_zone zone="homepage-header"]

// Sidebar
[banner_ads_zone zone="sidebar"]

// Custom zone
[banner_ads_zone zone="my-custom-zone" limit="2"]
```

### **Theme Integration:**
```php
// In your theme files
<?php if (function_exists('do_shortcode')) : ?>
    <?php echo do_shortcode('[banner_ads_zone zone="homepage-header"]'); ?>
<?php endif; ?>
```

## 🛠 **Admin Maintenance**

### **Regular Tasks:**
1. **Review pending campaigns** (if approval enabled)
2. **Check expired campaigns** 
3. **Monitor zone capacity**
4. **Review analytics** for performance
5. **Clean up old data** (Settings page)

### **Troubleshooting:**
1. **Campaign not showing?**
   - Check status is "Active"
   - Verify start/end dates
   - Confirm zone exists

2. **Upload issues?**
   - Check file size limits (Settings)
   - Verify file permissions
   - Check allowed file types

3. **Analytics not tracking?**
   - Verify JavaScript is loading
   - Check browser console for errors
   - Ensure proper shortcode placement

## 🎯 **Admin vs User Differences**

| Feature | Admin | Regular User |
|---------|-------|--------------|
| Campaign Creation | Direct admin panel | Upload form only |
| Payment Required | ❌ No | ✅ Yes |
| Auto-Activation | ✅ Yes | ❌ No (needs purchase) |
| Status Control | ✅ Full control | ❌ Limited |
| Date Control | ✅ Custom dates | ❌ Package-based |
| Zone Access | ✅ All zones | ✅ Active zones only |
| Analytics Access | ✅ All campaigns | ✅ Own campaigns only |

## 🚨 **Important Notes**

1. **Admin campaigns bypass payment** - No WooCommerce order required
2. **Default duration** - Admin uploads get 30 days (customizable)
3. **Status override** - Admins can set any status directly
4. **Zone management** - Only admins can create/edit zones
5. **Analytics access** - Admins see all campaign data

Your banner ads system is now fully configured for admin use! 🎉
