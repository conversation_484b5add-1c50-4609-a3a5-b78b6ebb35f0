<?php
/**
 * Test script for campaign creation
 */

// WordPress environment
require_once('../../../wp-config.php');

// Include required classes
require_once('models/class-banner-zone.php');
require_once('models/class-banner-campaign.php');

echo "<h1>Campaign Creation Test</h1>";

// Check if user is logged in
if (!is_user_logged_in()) {
    echo "<p style='color: red;'>You must be logged in to test campaign creation.</p>";
    echo "<p><a href='" . wp_login_url() . "'>Login</a></p>";
    exit;
}

$user_id = get_current_user_id();
echo "<p>Logged in as user ID: {$user_id}</p>";

// Check zones
$zones = Banner_Zone::get_options(true);
echo "<h2>Available Zones:</h2>";
if (empty($zones)) {
    echo "<p style='color: red;'>No zones available!</p>";
} else {
    echo "<ul>";
    foreach ($zones as $zone_id => $zone_name) {
        echo "<li><strong>{$zone_id}</strong>: {$zone_name}</li>";
    }
    echo "</ul>";
}

// Test campaign creation
if (isset($_POST['test_campaign'])) {
    echo "<h2>Testing Campaign Creation...</h2>";
    
    $form_data = array(
        'title' => 'Test Campaign',
        'banner_type' => 'html',
        'zone' => 'homepage-header',
        'user_id' => $user_id,
        'link_url' => 'https://example.com',
        'link_target' => '_blank',
        'html_content' => '<div style="background: #f0f0f0; padding: 20px; text-align: center;">Test Banner</div>'
    );
    
    echo "<p>Form data:</p>";
    echo "<pre>" . print_r($form_data, true) . "</pre>";
    
    $campaign = Banner_Campaign::create_from_form($form_data);
    
    if (is_wp_error($campaign)) {
        echo "<p style='color: red;'>Campaign creation failed: " . $campaign->get_error_message() . "</p>";
    } else {
        echo "<p style='color: green;'>Campaign created successfully!</p>";
        echo "<p>Campaign ID: {$campaign->id}</p>";
        echo "<p>Campaign Title: {$campaign->title}</p>";
        echo "<p>Campaign Status: {$campaign->status}</p>";
    }
}

?>

<h2>Test Campaign Creation</h2>
<form method="post">
    <p>
        <button type="submit" name="test_campaign" style="background: #0073aa; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
            Create Test Campaign
        </button>
    </p>
</form>

<h2>AJAX Test</h2>
<p>Test the AJAX endpoint directly:</p>
<button id="test-ajax" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer;">
    Test AJAX Campaign Creation
</button>

<div id="ajax-result" style="margin-top: 20px; padding: 10px; border: 1px solid #ddd; display: none;"></div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    $('#test-ajax').on('click', function() {
        var $button = $(this);
        var $result = $('#ajax-result');
        
        $button.prop('disabled', true).text('Testing...');
        $result.show().html('Sending AJAX request...');
        
        var formData = new FormData();
        formData.append('action', 'upload_banner_campaign');
        formData.append('nonce', '<?php echo wp_create_nonce('banner_upload_nonce'); ?>');
        formData.append('campaign_title', 'AJAX Test Campaign');
        formData.append('banner_type', 'html');
        formData.append('banner_zone', 'homepage-header');
        formData.append('link_url', 'https://example.com');
        formData.append('link_target', '_blank');
        formData.append('html_content', '<div style="background: #e0e0e0; padding: 15px; text-align: center;">AJAX Test Banner</div>');
        
        $.ajax({
            url: '<?php echo admin_url('admin-ajax.php'); ?>',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                console.log('AJAX Response:', response);
                if (response.success) {
                    $result.html('<div style="color: green;">✓ AJAX Success: ' + response.data.message + '</div>');
                } else {
                    $result.html('<div style="color: red;">✗ AJAX Error: ' + (response.data || 'Unknown error') + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                console.log('Response Text:', xhr.responseText);
                $result.html('<div style="color: red;">✗ Network Error: ' + error + '</div><pre>' + xhr.responseText + '</pre>');
            },
            complete: function() {
                $button.prop('disabled', false).text('Test AJAX Campaign Creation');
            }
        });
    });
});
</script>

<p><a href="http://ads-manager.local/my-account/upload-banner/">Go to Upload Page</a></p>
