# Banner Ads Plugin - Test Workflow Guide

## 🧪 **Step-by-Step Testing Process**

### **Phase 1: Setup Test Pages**

1. **Create Upload Page**
   - Go to **Pages > Add New**
   - Title: "Upload Banner Ad"
   - Content: `[banner_ads_upload]`
   - Publish the page

2. **Create Dashboard Page**
   - Go to **Pages > Add New**
   - Title: "My Banner Campaigns"
   - Content: `[banner_ads_dashboard]`
   - Publish the page

3. **Create Test Banner Zone Page**
   - Go to **Pages > Add New**
   - Title: "Test Banner Display"
   - Content: `[banner_ads_zone zone="homepage-header"]`
   - Publish the page

### **Phase 2: Create Test User**

1. **Create Real Estate Agent Account**
   - Go to **Users > Add New**
   - Username: `test-agent`
   - Email: `<EMAIL>`
   - Role: `Subscriber` or `Customer`
   - Send login details

### **Phase 3: Test the Complete Workflow**

#### **Step 1: Upload Banner (As Test User)**
1. Log in as test user
2. Go to "Upload Banner Ad" page
3. Fill out the form:
   - Campaign Title: "Test Real Estate Banner"
   - Banner Type: Image
   - Upload a test image (JPG/PNG)
   - Link URL: `https://example.com`
   - Zone: Homepage Header
4. Click "Submit Banner Campaign"
5. **Expected Result**: Success message + campaign created with "pending" status

#### **Step 2: Verify Campaign Created**
1. Log in as admin
2. Go to **Banner Ads > All Campaigns**
3. **Expected Result**: See "Test Real Estate Banner" with status "Pending"

#### **Step 3: Purchase Package (As Test User)**
1. Log in as test user
2. Go to **Shop** page
3. Add "Banner Ad - 1 Week" to cart
4. Complete checkout (use $0 price for testing)
5. **Expected Result**: Order completed

#### **Step 4: Verify Auto-Activation**
1. Go to **WooCommerce > Orders**
2. Mark the order as "Completed" if not already
3. Go to **Banner Ads > All Campaigns**
4. **Expected Result**: Campaign status should change to "Active"

#### **Step 5: Test Banner Display**
1. Go to "Test Banner Display" page
2. **Expected Result**: Banner should be visible

#### **Step 6: Test Dashboard**
1. Log in as test user
2. Go to "My Banner Campaigns" page
3. **Expected Result**: See campaign with stats

## 🔧 **Troubleshooting Common Issues**

### **Issue 1: Upload Form Not Working**
**Symptoms**: Form doesn't submit or shows errors
**Solutions**:
- Check if user is logged in
- Verify file upload permissions
- Check PHP error logs

### **Issue 2: Campaign Not Activating**
**Symptoms**: Campaign stays "pending" after purchase
**Solutions**:
- Manually mark WooCommerce order as "Completed"
- Check if WooCommerce hooks are firing
- Verify campaign belongs to the purchasing user

### **Issue 3: Banner Not Displaying**
**Symptoms**: Shortcode shows but no banner appears
**Solutions**:
- Check if campaign is "active"
- Verify campaign dates (start/end)
- Check zone configuration

### **Issue 4: Dashboard Shows No Campaigns**
**Symptoms**: User dashboard is empty
**Solutions**:
- Verify user is logged in
- Check if campaigns belong to current user
- Verify campaign post type is published

## 🛠 **Manual Testing Commands**

### **Check Campaign Status (Admin)**
```php
// Add to functions.php temporarily for testing
add_action('wp_footer', function() {
    if (current_user_can('manage_options')) {
        $campaigns = get_posts(array(
            'post_type' => 'banner_campaign',
            'posts_per_page' => -1,
            'post_status' => 'publish'
        ));
        
        echo '<div style="background: white; padding: 20px; margin: 20px; border: 1px solid #ccc;">';
        echo '<h3>Debug: All Campaigns</h3>';
        foreach ($campaigns as $campaign) {
            $status = get_post_meta($campaign->ID, '_campaign_status', true);
            $zone = get_post_meta($campaign->ID, '_banner_zone', true);
            $start = get_post_meta($campaign->ID, '_campaign_start_date', true);
            $end = get_post_meta($campaign->ID, '_campaign_end_date', true);
            
            echo "<p><strong>{$campaign->post_title}</strong><br>";
            echo "Status: {$status}<br>";
            echo "Zone: {$zone}<br>";
            echo "Author: {$campaign->post_author}<br>";
            echo "Start: {$start}<br>";
            echo "End: {$end}</p><hr>";
        }
        echo '</div>';
    }
});
```

### **Force Campaign Activation (Admin)**
```php
// Add to functions.php temporarily
add_action('wp_footer', function() {
    if (current_user_can('manage_options') && isset($_GET['activate_campaign'])) {
        $campaign_id = intval($_GET['activate_campaign']);
        $start_date = current_time('Y-m-d H:i:s');
        $end_date = date('Y-m-d H:i:s', strtotime($start_date . ' + 7 days'));
        
        update_post_meta($campaign_id, '_campaign_status', 'active');
        update_post_meta($campaign_id, '_campaign_start_date', $start_date);
        update_post_meta($campaign_id, '_campaign_end_date', $end_date);
        
        echo '<div style="background: green; color: white; padding: 10px;">Campaign activated!</div>';
    }
});
```

## 📋 **Quick Checklist**

- [ ] Upload form page created with shortcode
- [ ] Dashboard page created with shortcode
- [ ] Test user account created
- [ ] Banner uploaded successfully
- [ ] Campaign appears in admin with "pending" status
- [ ] Package purchased and order completed
- [ ] Campaign status changed to "active"
- [ ] Banner displays on frontend
- [ ] Dashboard shows campaign data

## 🎯 **Expected User Journey**

1. **User visits upload page** → Uploads banner → Gets "pending" campaign
2. **User visits shop** → Buys package → Order completed
3. **System automatically** → Activates campaign → Sets dates
4. **User visits dashboard** → Sees active campaign → Views stats
5. **Visitors see banner** → Analytics tracked → User sees results

## 🚨 **Common Mistakes**

1. **Wrong Order**: Buying package BEFORE uploading banner
2. **User Mismatch**: Campaign created by different user than purchaser
3. **Order Status**: Order not marked as "Completed"
4. **Zone Issues**: Banner uploaded to inactive zone
5. **Date Issues**: Campaign dates not set properly

Follow this workflow step by step, and the system should work correctly!
