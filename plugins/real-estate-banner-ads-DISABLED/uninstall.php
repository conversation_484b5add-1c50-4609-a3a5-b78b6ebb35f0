<?php
/**
 * Fired when the plugin is uninstalled.
 *
 * @package RealEstateBannerAds
 */

// If uninstall not called from WordPress, then exit.
if ( ! defined( 'WP_UNINSTALL_PLUGIN' ) ) {
	exit;
}

/**
 * Delete plugin data on uninstall
 */
function real_estate_banner_ads_uninstall() {
	global $wpdb;

	// Delete custom post type posts
	$campaigns = get_posts( array(
		'post_type' => 'banner_campaign',
		'numberposts' => -1,
		'post_status' => 'any'
	));

	foreach ( $campaigns as $campaign ) {
		wp_delete_post( $campaign->ID, true );
	}

	// Delete custom taxonomy terms
	$terms = get_terms( array(
		'taxonomy' => 'campaign_status',
		'hide_empty' => false
	));

	foreach ( $terms as $term ) {
		wp_delete_term( $term->term_id, 'campaign_status' );
	}

	// Drop custom tables
	$table_impressions = $wpdb->prefix . 'banner_impressions';
	$table_clicks = $wpdb->prefix . 'banner_clicks';
	
	$wpdb->query( "DROP TABLE IF EXISTS $table_impressions" );
	$wpdb->query( "DROP TABLE IF EXISTS $table_clicks" );

	// Delete plugin options
	$options_to_delete = array(
		'banner_ads_version',
		'banner_ads_db_version',
		'banner_ads_zones',
		'banner_ads_auto_approve',
		'banner_ads_max_file_size',
		'banner_ads_allowed_file_types',
		'banner_ads_email_notifications',
		'banner_ads_products_created'
	);

	foreach ( $options_to_delete as $option ) {
		delete_option( $option );
	}

	// Clear scheduled events
	wp_clear_scheduled_hook( 'banner_ads_expire_campaigns' );
	wp_clear_scheduled_hook( 'banner_ads_cleanup_analytics' );

	// Delete uploaded banner files
	$upload_dir = wp_upload_dir();
	$banner_upload_dir = $upload_dir['basedir'] . '/banner-ads/';
	
	if ( is_dir( $banner_upload_dir ) ) {
		real_estate_banner_ads_delete_directory( $banner_upload_dir );
	}

	// Flush rewrite rules
	flush_rewrite_rules();
}

/**
 * Recursively delete directory and its contents
 *
 * @param string $dir Directory path.
 * @return bool True on success, false on failure.
 */
function real_estate_banner_ads_delete_directory( $dir ) {
	if ( ! is_dir( $dir ) ) {
		return false;
	}

	$files = array_diff( scandir( $dir ), array( '.', '..' ) );
	
	foreach ( $files as $file ) {
		$file_path = $dir . DIRECTORY_SEPARATOR . $file;
		
		if ( is_dir( $file_path ) ) {
			real_estate_banner_ads_delete_directory( $file_path );
		} else {
			unlink( $file_path );
		}
	}
	
	return rmdir( $dir );
}

// Run uninstall
real_estate_banner_ads_uninstall();
